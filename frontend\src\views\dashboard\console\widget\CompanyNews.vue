<template>
  <div class="company-news-widget art-custom-card">
    <div class="widget-header">
      <h4 class="box-title">新闻资讯</h4>
      <el-button text type="primary" size="small" @click="viewAllNews"> 查看更多 </el-button>
    </div>

    <div class="news-content" v-loading="loading">
      <div v-if="newsList.length > 0" class="news-list">
        <div
          v-for="news in newsList"
          :key="news.id"
          class="news-item"
          @click="viewNewsDetail(news)"
        >
          <!-- 封面图片区域 -->
          <div class="news-cover">
            <el-image
              :src="getCoverImage(news.cover_image_arr)"
              fit="cover"
              loading="lazy"
              class="cover-image"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                  <span>无图像</span>
                </div>
              </template>
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                  <span>无图像</span>
                </div>
              </template>
            </el-image>
            <!-- 置顶标签 -->
            <!--            <div v-if="news.is_top" class="top-badge">
                          <el-tag type="warning" size="small" effect="plain">置顶</el-tag>
                        </div>-->
          </div>

          <!-- 内容区域 -->
          <div class="news-content-area">
            <div class="news-header">
              <div class="news-title">{{ news.title }}</div>
              <div class="news-badges">
                <el-tag v-if="news.is_important" type="danger" size="small">
                  重要
                </el-tag>
              </div>
            </div>

            <!-- 分类和发布时间 -->
            <div class="news-category-time">
              <span v-if="news.category_name" class="category-tag">
                <el-icon><Folder /></el-icon>
                {{ news.category_name }}
              </span>
              <span class="publish-time">
                <el-icon><Clock /></el-icon>
                {{ formatPublishTime(news.publish_time) }}
              </span>
            </div>

            <div class="news-summary" v-if="news.summary">
              {{ news.summary }}
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty :image-size="60" description="暂无数据">
          <el-button @click="loadCompanyNews" type="primary" size="small"> 刷新数据 </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 企业资讯详情组件 -->
    <ArticleDetail ref="articleDetailRef" :show-actions="false" @success="loadCompanyNews" />
  </div>
</template>

<script setup lang="ts">
  import { WorkbenchApi, type CompanyNews } from '@/api/dashboard/workbenchApi'
  import { useRouter } from 'vue-router'
  import { Picture, Folder, Clock } from '@element-plus/icons-vue'
  import ArticleDetail from '@/components/system/ArticleDetail.vue'

  // 扩展CompanyNews类型以包含新字段
  interface ExtendedCompanyNews extends CompanyNews {
    cover_image_arr?: string[]
    is_top?: number
    category_name?: string
    publish_time?: string
  }

  const router = useRouter()

  const newsList = ref<ExtendedCompanyNews[]>([])
  const loading = ref(false)
  const articleDetailRef = ref()

  /**
   * 获取封面图片
   */
  const getCoverImage = (coverImages?: string[]): string => {
    if (coverImages && coverImages.length > 0) {
      return coverImages[0]
    }
    return ''
  }

  /**
   * 格式化发布时间
   */
  const formatPublishTime = (publishTime?: string): string => {
    if (!publishTime) return ''

    try {
      const date = new Date(publishTime)
      const now = new Date()
      const diffTime = now.getTime() - date.getTime()
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      }
    } catch (error) {
      return publishTime
    }
  }

  /**
   * 加载企业新闻数据
   */
  const loadCompanyNews = async () => {
    try {
      loading.value = true
      const res = await WorkbenchApi.getCompanyNews({ limit: 5 })

      if (res.code === 1) {
        newsList.value = res.data as ExtendedCompanyNews[]
      }
    } catch (error) {
      console.error('加载企业新闻失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 查看所有新闻
   */
  const viewAllNews = () => {
    router.push('/office/article/article_list')
  }

  /**
   * 查看新闻详情
   */
  const viewNewsDetail = (news: ExtendedCompanyNews) => {
    // 打开详情弹窗
    articleDetailRef.value?.showDetail(news.id)
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadCompanyNews()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadCompanyNews
  })
</script>

<style lang="scss" scoped>
  .company-news-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }
    }

    .news-content {
      height: calc(100% - 60px);
      overflow: hidden;
    }

    .news-list {
      height: 100%;
      overflow-y: auto;

      .news-item {
        display: flex;
        gap: 12px;
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
          //transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .news-cover {
          position: relative;
          flex-shrink: 0;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;

          .cover-image {
            width: 100%;
            height: 100%;
            border-radius: 6px;
          }

          .image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: var(--el-fill-color-light);
            color: var(--el-text-color-placeholder);
            font-size: 12px;
            gap: 4px;

            .el-icon {
              font-size: 20px;
            }
          }

          .top-badge {
            position: absolute;
            top: 6px;
            left: 6px;
          }
        }

        .news-content-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8px;
          min-width: 0;

          .news-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 12px;

            .news-title {
              flex: 1;
              font-size: 14px;
              font-weight: 500;
              color: var(--art-text-color-1);
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .news-badges {
              flex-shrink: 0;
            }
          }

          .news-category-time {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--art-text-color-3);

            .category-tag,
            .publish-time {
              display: flex;
              align-items: center;
              gap: 4px;

              .el-icon {
                font-size: 14px;
              }
            }

            .category-tag {
              color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
              padding: 2px 6px;
              border-radius: 4px;
            }
          }

          .news-summary {
            font-size: 13px;
            color: var(--art-text-color-2);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }

    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .company-news-widget {
      height: auto;
      min-height: 300px;

      .news-content {
        height: auto;
        min-height: 240px;
      }

      .news-list {
        .news-item {
          flex-direction: column;
          padding: 12px;
          margin-bottom: 8px;
          gap: 8px;

          .news-cover {
            width: 100%;
            height: 120px;

            .top-badge {
              top: 4px;
              left: 4px;
            }
          }

          .news-content-area {
            gap: 6px;

            .news-header {
              margin-bottom: 0;
              gap: 8px;

              .news-title {
                font-size: 13px;
              }
            }

            .news-category-time {
              gap: 12px;
              font-size: 11px;

              .category-tag {
                padding: 1px 4px;
              }
            }

            .news-summary {
              font-size: 12px;
              -webkit-line-clamp: 3;
            }
          }
        }
      }
    }
  }
</style>

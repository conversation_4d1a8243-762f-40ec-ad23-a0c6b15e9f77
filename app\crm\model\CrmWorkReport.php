<?php
declare(strict_types=1);

namespace app\crm\model;

use app\common\core\base\BaseModel;
use app\common\core\traits\model\CreatorTrait;

/**
 * 工作报告表模型
 */
class CrmWorkReport extends BaseModel
{
	use CreatorTrait;
	
	// 设置表名
	protected $name = 'crm_work_report';
	
	protected $append = [
		'creator'
	];
	
	public function getAttachmentsAttr($value)
	{
		return !empty($value)
			? explode(',', $value)
			: [];
	}
}
<template>
  <div class="news-demo-page">
    <div class="page-header">
      <h1>新闻资讯UI美化效果展示</h1>
      <p>展示改进后的新闻资讯组件，包含封面图片、分类标签、发布时间等功能</p>
    </div>

    <div class="demo-container">
      <!-- 原始样式对比 -->
      <div class="demo-section">
        <h2>改进后的新闻资讯组件</h2>
        <div class="demo-content">
          <CompanyNews />
        </div>
      </div>

      <!-- 模拟数据展示 -->
      <div class="demo-section">
        <h2>模拟数据效果预览</h2>
        <div class="demo-content">
          <div class="company-news-widget art-custom-card">
            <div class="widget-header">
              <h4 class="box-title">新闻资讯</h4>
              <el-button text type="primary" size="small"> 查看更多 </el-button>
            </div>

            <div class="news-content">
              <div class="news-list">
                <div
                  v-for="news in mockNewsList"
                  :key="news.id"
                  class="news-item"
                >
                  <!-- 封面图片区域 -->
                  <div class="news-cover">
                    <el-image 
                      :src="news.cover_image[0]" 
                      fit="cover" 
                      loading="lazy"
                      class="cover-image"
                    >
                      <template #placeholder>
                        <div class="image-placeholder">
                          <el-icon><Picture /></el-icon>
                          <span>无图像</span>
                        </div>
                      </template>
                      <template #error>
                        <div class="image-placeholder">
                          <el-icon><Picture /></el-icon>
                          <span>无图像</span>
                        </div>
                      </template>
                    </el-image>
                    <!-- 置顶标签 -->
                    <div v-if="news.is_top" class="top-badge">
                      <el-tag type="warning" size="small" effect="plain">置顶</el-tag>
                    </div>
                  </div>

                  <!-- 内容区域 -->
                  <div class="news-content-area">
                    <div class="news-header">
                      <div class="news-title">{{ news.title }}</div>
                      <div class="news-badges">
                        <el-tag v-if="news.is_important" type="danger" size="small" effect="plain">
                          重要
                        </el-tag>
                      </div>
                    </div>
                    
                    <!-- 分类和发布时间 -->
                    <div class="news-category-time">
                      <span v-if="news.category_name" class="category-tag">
                        <el-icon><Folder /></el-icon>
                        {{ news.category_name }}
                      </span>
                      <span class="publish-time">
                        <el-icon><Clock /></el-icon>
                        {{ news.publish_time }}
                      </span>
                    </div>

                    <div class="news-summary" v-if="news.summary">
                      {{ news.summary }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="demo-section">
        <h2>改进功能说明</h2>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon class="feature-icon"><Picture /></el-icon>
            <div class="feature-content">
              <h3>封面图片显示</h3>
              <p>选择cover_image数组的第一张图片作为封面，如果没有则显示"无图像"占位符</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Clock /></el-icon>
            <div class="feature-content">
              <h3>发布时间优化</h3>
              <p>使用publish_time字段而不是created_at，并提供智能时间格式化（今天、昨天、X天前等）</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Folder /></el-icon>
            <div class="feature-content">
              <h3>分类标签</h3>
              <p>显示category_name分类名称，带有图标和特殊样式</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Star /></el-icon>
            <div class="feature-content">
              <h3>置顶和重要标识</h3>
              <p>支持置顶标签和重要标签的显示，提升内容层级</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><View /></el-icon>
            <div class="feature-content">
              <h3>响应式布局</h3>
              <p>在移动端自动调整为垂直布局，确保在各种设备上的良好显示效果</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Picture, Folder, Clock, Star, View } from '@element-plus/icons-vue'
  import CompanyNews from '@/views/dashboard/console/widget/CompanyNews.vue'

  // 模拟新闻数据
  const mockNewsList = ref([
    {
      id: 1,
      title: "阿斯顿发斯蒂芬",
      summary: "asdfasdf",
      is_top: 1,
      created_at: "2025-06-30 14:27:37",
      cover_image: [
        "http://www.bs.com/uploads/2025/06/29/1fa23e764759da7be489d7bab40336a6.png"
      ],
      publish_time: "2025-06-25",
      is_important: 1,
      category_id: 12,
      category_name: "test"
    },
    {
      id: 2,
      title: "企业发展新动态：技术创新引领未来",
      summary: "公司在人工智能和大数据领域取得重大突破，为客户提供更优质的服务体验。",
      is_top: 0,
      created_at: "2025-06-29 10:15:22",
      cover_image: [],
      publish_time: "2025-06-29",
      is_important: 0,
      category_id: 13,
      category_name: "企业动态"
    },
    {
      id: 3,
      title: "行业峰会成功举办，共话数字化转型",
      summary: "本次峰会汇聚了行业内顶尖专家，深入探讨了数字化转型的最新趋势和实践经验。",
      is_top: 1,
      created_at: "2025-06-28 16:30:45",
      cover_image: [
        "https://picsum.photos/400/300?random=1"
      ],
      publish_time: "2025-06-28",
      is_important: 1,
      category_id: 14,
      category_name: "行业资讯"
    }
  ])
</script>

<style lang="scss" scoped>
  .news-demo-page {
    padding: 20px;
    background: var(--art-main-bg-color);
    min-height: 100vh;

    .page-header {
      text-align: center;
      margin-bottom: 40px;
      
      h1 {
        font-size: 28px;
        color: var(--art-text-color-1);
        margin-bottom: 10px;
      }
      
      p {
        font-size: 16px;
        color: var(--art-text-color-2);
      }
    }

    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: 40px;
      
      h2 {
        font-size: 20px;
        color: var(--art-text-color-1);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--el-color-primary);
      }
    }

    .demo-content {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .feature-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .feature-icon {
          font-size: 24px;
          color: var(--el-color-primary);
          flex-shrink: 0;
          margin-top: 2px;
        }

        .feature-content {
          h3 {
            font-size: 16px;
            color: var(--art-text-color-1);
            margin-bottom: 8px;
          }

          p {
            font-size: 14px;
            color: var(--art-text-color-2);
            line-height: 1.5;
          }
        }
      }
    }
  }

  // 引入新闻组件的样式
  .company-news-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }
    }

    .news-content {
      height: calc(100% - 60px);
      overflow: hidden;
    }

    .news-list {
      height: 100%;
      overflow-y: auto;

      .news-item {
        display: flex;
        gap: 12px;
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .news-cover {
          position: relative;
          flex-shrink: 0;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;

          .cover-image {
            width: 100%;
            height: 100%;
            border-radius: 6px;
          }

          .image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: var(--el-fill-color-light);
            color: var(--el-text-color-placeholder);
            font-size: 12px;
            gap: 4px;

            .el-icon {
              font-size: 20px;
            }
          }

          .top-badge {
            position: absolute;
            top: 6px;
            left: 6px;
          }
        }

        .news-content-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8px;
          min-width: 0;

          .news-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 12px;

            .news-title {
              flex: 1;
              font-size: 14px;
              font-weight: 500;
              color: var(--art-text-color-1);
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .news-badges {
              flex-shrink: 0;
            }
          }

          .news-category-time {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--art-text-color-3);

            .category-tag,
            .publish-time {
              display: flex;
              align-items: center;
              gap: 4px;

              .el-icon {
                font-size: 14px;
              }
            }

            .category-tag {
              color: var(--el-color-primary);
              background: var(--el-color-primary-light-9);
              padding: 2px 6px;
              border-radius: 4px;
            }
          }

          .news-summary {
            font-size: 13px;
            color: var(--art-text-color-2);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }
</style>

<?php
/**
 * 调试角色菜单关联问题
 */

// 引入ThinkPHP框架
require __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

try {
    echo "=== 角色菜单关联调试 ===\n\n";
    
    // 1. 检查数据库连接
    echo "1. 检查数据库连接...\n";
    $dbConfig = Db::getConfig();
    echo "数据库配置：" . json_encode($dbConfig, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // 2. 查看角色表数据
    echo "2. 查看角色表数据...\n";
    $roles = Db::table('system_role')
        ->where('tenant_id', 1)
        ->where('status', 1)
        ->limit(5)
        ->select();
    
    echo "角色数量：" . count($roles) . "\n";
    foreach ($roles as $role) {
        echo "角色ID：{$role['id']}, 名称：{$role['name']}, 租户ID：{$role['tenant_id']}\n";
    }
    echo "\n";
    
    // 3. 查看角色菜单关联表数据
    echo "3. 查看角色菜单关联表数据...\n";
    $roleMenus = Db::table('system_role_menu')
        ->where('tenant_id', 1)
        ->limit(10)
        ->select();
    
    echo "角色菜单关联数量：" . count($roleMenus) . "\n";
    foreach ($roleMenus as $rm) {
        echo "关联ID：{$rm['id']}, 角色ID：{$rm['role_id']}, 菜单ID：{$rm['menu_id']}, 租户ID：{$rm['tenant_id']}\n";
    }
    echo "\n";
    
    // 4. 查看菜单表数据
    echo "4. 查看菜单表数据（前10个）...\n";
    $menus = Db::table('system_menu')
        ->where('status', 1)
        ->limit(10)
        ->select();
    
    echo "菜单数量：" . count($menus) . "\n";
    foreach ($menus as $menu) {
        echo "菜单ID：{$menu['id']}, 标题：{$menu['title']}, 类型：{$menu['type']}\n";
    }
    echo "\n";
    
    // 5. 检查特定角色的菜单权限
    if (!empty($roles)) {
        $testRoleId = $roles[0]['id'];
        echo "5. 检查角色ID {$testRoleId} 的菜单权限...\n";
        
        $roleMenuCount = Db::table('system_role_menu')
            ->where('role_id', $testRoleId)
            ->where('tenant_id', 1)
            ->count();
        
        echo "该角色的菜单权限数量：{$roleMenuCount}\n";
        
        if ($roleMenuCount > 0) {
            $roleMenuIds = Db::table('system_role_menu')
                ->where('role_id', $testRoleId)
                ->where('tenant_id', 1)
                ->column('menu_id');
            
            echo "菜单ID列表：" . implode(', ', $roleMenuIds) . "\n";
        }
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 调试过程中发生错误：" . $e->getMessage() . "\n";
    echo "错误文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈：\n" . $e->getTraceAsString() . "\n";
}

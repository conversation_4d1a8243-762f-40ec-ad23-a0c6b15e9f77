<?php

use app\common\middleware\TokenAuthMiddleware;
use app\common\middleware\PermissionMiddleware;
use think\facade\Route;

/**
 * 工作台路由配置
 */
Route::group('api/dashboard', function () {
	
	$nameSpace = 'app\dashboard\controller';
	
	// 获取关键指标统计
	Route::get('getKeyStatistics', $nameSpace . '\WorkbenchController@getKeyStatistics');
	
	// 获取聚合待办任务
	Route::get('getTodoTasks', $nameSpace . '\WorkbenchController@getTodoTasks');
	
	// 获取工作汇报摘要
	Route::get('getWorkReports', $nameSpace . '\WorkbenchController@getWorkReports');
	
	// 获取企业新闻
	Route::get('getCompanyNews', $nameSpace . '\WorkbenchController@getCompanyNews');
	
	// 获取工作台所有数据（一次性接口）
	Route::get('getWorkbenchData', $nameSpace . '\WorkbenchController@getWorkbenchData');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     //    PermissionMiddleware::class
     ]);

<?php
declare(strict_types=1);

namespace app\common\utils;

use app\common\exception\BusinessException;
use app\system\service\RoleService;
use think\facade\Log;

/**
 * 数据权限缓存工具类
 * 支持按用户+租户组合缓存数据权限结果
 */
class DataPermissionCacheUtil
{
	/**
	 * 缓存过期时间（秒）- 默认1小时
	 */
	private const CACHE_EXPIRE = 3600;
	
	/**
	 * 缓存键前缀
	 */
	private const CACHE_KEY_PREFIX = 'data_permission';
	
	/**
	 * 获取用户数据权限
	 *
	 * @param bool $forceRefresh 是否强制刷新缓存
	 * @return array 数据权限配置
	 */
	public static function getUserDataPermission($userId, $tenantId, bool $forceRefresh = false): array
	{
		
		$cacheKey = self::CACHE_KEY_PREFIX . ':user:' . $userId . ':tenant:' . $tenantId;
		
		// 强制刷新或缓存不存在时，重新加载数据权限
		if ($forceRefresh || !CacheUtil::has($cacheKey)) {
			return self::refreshUserDataPermission($userId, $tenantId);
		}
		
		// 从缓存获取
		return CacheUtil::get($cacheKey, []);
	}
	
	/**
	 * 刷新用户数据权限缓存
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @return array 数据权限配置
	 */
	public static function refreshUserDataPermission($userId, $tenantId): array
	{
		$cacheKey = self::CACHE_KEY_PREFIX . ':user:' . $userId . ':' . $tenantId;
		
		try {
			// 从数据库加载最新的权限数据
			$dataPermission = self::loadUserDataPermissionFromDb($userId, $tenantId);
			
			// 写入缓存 - 使用标签来分组管理缓存，便于精确清除
			CacheUtil::tag(self::CACHE_KEY_PREFIX)
			         ->set($cacheKey, $dataPermission, self::CACHE_EXPIRE);
			
			// 同时设置用户和租户级别的标签缓存，便于批量清除
			CacheUtil::tag(self::CACHE_KEY_PREFIX . ':user:' . $userId)
			         ->set($cacheKey, $dataPermission, self::CACHE_EXPIRE);
			
			CacheUtil::tag(self::CACHE_KEY_PREFIX . ':tenant:' . $tenantId)
			         ->set($cacheKey, $dataPermission, self::CACHE_EXPIRE);
			
			// 记录缓存刷新日志
			Log::info('数据权限缓存已刷新', [
				'user_id'          => $userId,
				'tenant_id'        => $tenantId,
				'permission_count' => count($dataPermission),
				'cache_key'        => $cacheKey
			]);
			
			return $dataPermission;
		}
		catch (\Exception $e) {
			Log::error('刷新数据权限缓存失败: ' . $e->getMessage(), [
				'user_id'   => $userId,
				'tenant_id' => $tenantId,
				'trace'     => $e->getTraceAsString()
			]);
			throw new BusinessException('刷新缓存失败');
		}
	}
	
	/**
	 * 从数据库加载用户数据权限
	 *
	 * @param int $adminId  管理员ID
	 * @param int $tenantId 租户ID
	 * @return array 数据权限配置
	 */
	private static function loadUserDataPermissionFromDb($adminId, $tenantId): array
	{
		try {
			return RoleService::getInstance()
			                  ->getDataScopeUserIds($adminId, $tenantId);
		}
		catch (\Exception $e) {
			Log::error('从数据库加载数据权限失败: ' . $e->getMessage(), [
				'admin_id'  => $adminId,
				'tenant_id' => $tenantId,
				'trace'     => $e->getTraceAsString()
			]);
			return [];
		}
	}
	
	/**
	 * 批量刷新用户数据权限缓存
	 * 适用于角色权限变更等影响多个用户的场景
	 *
	 * @param array $userIds  用户ID数组
	 * @param int   $tenantId 租户ID
	 * @return array 刷新结果统计
	 */
	public static function batchRefreshUserDataPermission(array $userIds, int $tenantId): array
	{
		$successCount = 0;
		$failureCount = 0;
		$errors       = [];
		
		foreach ($userIds as $userId) {
			try {
				self::refreshUserDataPermission($userId, $tenantId);
				$successCount++;
			}
			catch (\Exception $e) {
				$failureCount++;
				$errors[] = [
					'user_id' => $userId,
					'error'   => $e->getMessage()
				];
			}
		}
		
		$result = [
			'total'   => count($userIds),
			'success' => $successCount,
			'failure' => $failureCount,
			'errors'  => $errors
		];
		
		Log::info('批量刷新数据权限缓存完成', $result);
		
		return $result;
	}
	
	/**
	 * 清除指定租户的所有数据权限缓存
	 *
	 * @param int $tenantId 租户ID
	 * @return bool
	 */
	public static function clearTenantDataPermissionCache(int $tenantId): bool
	{
		try {
			// 使用标签清除指定租户的所有缓存
			CacheUtil::tag(self::CACHE_KEY_PREFIX . ':tenant:' . $tenantId)
			         ->clear();
			
			Log::info('已清除租户数据权限缓存', [
				'tenant_id' => $tenantId
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('清除租户数据权限缓存失败: ' . $e->getMessage(), [
				'tenant_id' => $tenantId,
				'trace'     => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 清除指定用户的数据权限缓存
	 *
	 * @param int      $userId   用户ID
	 * @param int|null $tenantId 租户ID，为null时清除该用户在所有租户的缓存
	 * @return bool
	 */
	public static function clearUserDataPermissionCache(int $userId, ?int $tenantId = null): bool
	{
		try {
			if ($tenantId !== null) {
				// 清除指定租户的用户缓存
				$cacheKey = self::CACHE_KEY_PREFIX . ':user:' . $userId . ':' . $tenantId;
				CacheUtil::delete($cacheKey);
			}
			else {
				// 清除该用户在所有租户的缓存，使用用户标签
				CacheUtil::tag(self::CACHE_KEY_PREFIX . ':user:' . $userId)
				         ->clear();
			}
			
			Log::info('已清除用户数据权限缓存', [
				'user_id'   => $userId,
				'tenant_id' => $tenantId
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('清除用户数据权限缓存失败: ' . $e->getMessage(), [
				'user_id'   => $userId,
				'tenant_id' => $tenantId,
				'trace'     => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 权限变更事件处理
	 * 当角色、部门、用户权限发生变更时调用此方法
	 *
	 * @param string $eventType    事件类型：role_changed, dept_changed, user_changed
	 * @param array  $affectedData 受影响的数据
	 * @return bool
	 */
	public static function handlePermissionChangeEvent(string $eventType, array $affectedData): bool
	{
		try {
			switch ($eventType) {
				case 'role_changed':
					// 角色权限变更，需要刷新所有使用该角色的用户缓存
					if (isset($affectedData['role_id'], $affectedData['tenant_id'])) {
						self::refreshRoleUsersCache($affectedData['role_id'], $affectedData['tenant_id']);
					}
					break;
				
				case 'dept_changed':
					// 部门变更，需要刷新该部门及子部门的所有用户缓存
					if (isset($affectedData['dept_id'], $affectedData['tenant_id'])) {
						self::refreshDeptUsersCache($affectedData['dept_id'], $affectedData['tenant_id']);
					}
					break;
				
				case 'user_changed':
					// 用户权限变更，只需刷新该用户的缓存
					if (isset($affectedData['user_id'], $affectedData['tenant_id'])) {
						self::clearUserDataPermissionCache($affectedData['user_id'], $affectedData['tenant_id']);
					}
					break;
				
				default:
					Log::warning('未知的权限变更事件类型: ' . $eventType, $affectedData);
					return false;
			}
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('处理权限变更事件失败: ' . $e->getMessage(), [
				'event_type'    => $eventType,
				'affected_data' => $affectedData,
				'trace'         => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 刷新角色相关用户的缓存
	 *
	 * @param int $roleId   角色ID
	 * @param int $tenantId 租户ID
	 * @return void
	 */
	private static function refreshRoleUsersCache(int $roleId, int $tenantId): void
	{
		// 这里需要查询使用该角色的所有用户，然后刷新他们的缓存
		// 具体实现需要根据你的数据库结构来调整
		try {
			$userIds = \think\facade\Db::table('system_admin_role')
			                           ->where('role_id', $roleId)
			                           ->where('tenant_id', $tenantId)
			                           ->column('admin_id');
			
			if (!empty($userIds)) {
				self::batchRefreshUserDataPermission($userIds, $tenantId);
			}
		}
		catch (\Exception $e) {
			Log::error('刷新角色用户缓存失败: ' . $e->getMessage(), [
				'role_id'   => $roleId,
				'tenant_id' => $tenantId
			]);
		}
	}
	
	/**
	 * 刷新部门相关用户的缓存
	 *
	 * @param int $deptId   部门ID
	 * @param int $tenantId 租户ID
	 * @return void
	 */
	private static function refreshDeptUsersCache(int $deptId, int $tenantId): void
	{
		// 这里需要查询该部门及子部门的所有用户，然后刷新他们的缓存
		try {
			$userIds = \think\facade\Db::table('system_admin')
			                           ->where('dept_id', $deptId)
			                           ->where('tenant_id', $tenantId)
			                           ->column('id');
			
			if (!empty($userIds)) {
				self::batchRefreshUserDataPermission($userIds, $tenantId);
			}
		}
		catch (\Exception $e) {
			Log::error('刷新部门用户缓存失败: ' . $e->getMessage(), [
				'dept_id'   => $deptId,
				'tenant_id' => $tenantId
			]);
		}
	}
	
	/**
	 * 清除用户数据权限缓存
	 *
	 * @param $userId
	 * @return bool 是否成功
	 */
	public static function clearUserDataPermission($userId, $tenantId): bool
	{
		$cacheKey = self::CACHE_KEY_PREFIX . ':user:' . $userId . ':tenant:' . $tenantId;
		return CacheUtil::delete($cacheKey);
	}
	
	/**
	 * 清除所有数据权限缓存
	 *
	 * @return bool 是否成功
	 */
	public static function clearAllDataPermissions(): bool
	{
		return CacheUtil::tag(self::CACHE_KEY_PREFIX)
		                ->clear();
	}
} 
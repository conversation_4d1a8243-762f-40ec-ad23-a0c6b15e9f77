<?php
declare(strict_types=1);

namespace app\workflow\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\workflow\service\ModuleService;
use app\workflow\service\WorkflowTypeService;
use think\response\Json;

/**
 * 工作流程类型表控制器
 */
class TypeController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var WorkflowTypeService
	 */
	protected WorkflowTypeService $service;
	
	
	/**
	 * 初始化
	 */
	protected function initialize(): void
	{
		parent::initialize();
		$this->service = WorkflowTypeService::getInstance();
		$this->service->setEnableDataPermission(false);
	}
	
	
	/**
	 * 删除
	 */
	public function delete(): Json
	{
		$ids = $this->request->post('ids');
		if (!$ids) {
			return $this->error('参数错误');
		}
		$result = $this->service->batchDelete($ids);
		return $this->success('', $result);
	}
	
	/**
	 * 状态切换
	 */
	public function status($id): Json
	{
		$status = $this->request->post('status');
		$result = $this->service->updateStatus($id, $status);
		return $this->success($result);
	}
	
	// 获取模块下拉选项
	public function moduleOptions(): Json
	{
		return $this->success('', ModuleService::getModuleOptions());
	}
	
	// 获取业务下拉选项
	public function businessOptions(): Json
	{
		return $this->success('', $this->service->getBusinessOptions());
	}
	
	/**
	 * 获取表单字段：元数据
	 */
	public function getFormField($id): Json
	{
		$result = $this->service->getFormField($id);
		return $this->success('', $result);
	}
	
	/**
	 * 获取下拉选项
	 *
	 * @return Json
	 */
	public function options(): Json
	{
		$params     = $this->request->param();
		$labelField = $params['label_field'] ?? 'name';
		$valueField = $params['value_field'] ?? 'id';
		$where      = [
			'status' => 1
		];
		
		$result = $this->service->getSelectOptions($where, $labelField, $valueField);
		return $this->success('获取成功', $result);
	}
} 
<template>
  <el-drawer
    v-model="visible"
    :title="formData.id ? '编辑工作流程定义表' : '新增工作流程定义表'"
    :size="500"
    destroy-on-close
    append-to-body
  >
    <el-scrollbar height="calc(100% - 55px)">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="form-container"
      >
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>

        <el-form-item label="流程类型" prop="type_id">
          <el-select
            v-model="formData.type_id"
            style="width: 100%"
            placeholder="请选择流程类型"
            :loading="typeLoading"
            clearable
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!--        <el-form-item label="流程配置" prop="flow_config">
                  <el-input
                    v-model="formData.flow_config"
                    :autosize="{ minRows: 2 }"
                    type="textarea"
                    placeholder="流程配置"
                  />
                </el-form-item>-->

        <el-form-item label="图标" prop="icon">
          <el-input v-model="formData.icon" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :value="0" label="禁用">禁用</el-radio>
            <el-radio :value="1" label="启用">启用</el-radio>
          </el-radio-group>
        </el-form-item>

        <!--        <el-form-item label="是否为模板" prop="is_template">
                  <el-switch v-model="formData.is_template" :active-value="1" :inactive-value="0" />
                </el-form-item>-->

        <el-form-item label="说明" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
        <el-button type="success" @click="handleDesign" v-if="formType === 'edit'"
          >设计流程
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
  import { ElMessage, FormInstance } from 'element-plus'
  import { computed, nextTick, reactive, ref } from 'vue'
  import { WorkflowDefinitionApi } from '@/api/workflow/workflow_definition'
  import { formRules, getDefaultFormData, WorkflowDefinitionForm } from './form-helper'
  import { ApiStatus } from '@/utils/http/status'
  import { useRouter } from 'vue-router'

  // 路由
  const router = useRouter()

  // 对话框可见性
  const visible = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 加载状态
  const loading = ref(false)

  // 定义 props
  const props = defineProps<{
    typeOptions?: any[]
    typesLoading?: boolean
  }>()

  // 表单数据
  const formData = reactive<WorkflowDefinitionForm>(getDefaultFormData())

  // 流程类型选项 - 使用 props 或默认值
  const typeLoading = computed(() => props.typesLoading || false)
  const typeOptions = computed(() => props.typeOptions || [])

  // 表单类型（add-新增，edit-编辑）
  const formType = ref('add')

  // 显示新增表单
  const showAddForm = () => {
    formType.value = 'add'
    visible.value = true

    // 重置表单
    nextTick(() => {
      formRef.value?.resetFields()
      const defaultData = getDefaultFormData()
      // 将type_id设置为undefined以触发请选择提示
      defaultData.type_id = undefined
      Object.assign(formData, defaultData)
    })
  }

  // 显示编辑表单
  const showEditForm = async (id: number) => {
    formType.value = 'edit'
    visible.value = true
    loading.value = true

    try {
      // 获取详情
      const res = await WorkflowDefinitionApi.detail(id)

      if (res.code === ApiStatus.success) {
        // 填充表单
        await nextTick(() => {
          formRef.value?.resetFields()
          // 不使用全部合并，只需要formData内的字段
          // Object.assign(formData, res.data)
          formData.name = res.data.name
          formData.type_id = res.data.type_id
          formData.icon = res.data.icon
          formData.status = res.data.status
          formData.is_template = res.data.is_template
          formData.flow_config = res.data.flow_config
          formData.remark = res.data.remark
        })
      }
    } catch (error) {
      console.error('获取详情失败:', error)
      ElMessage.error('获取详情失败')
    } finally {
      loading.value = false
    }
  }

  // 提交表单
  const handleSubmit = () => {
    if (!formRef.value) return

    formRef.value.validate(async (valid) => {
      if (!valid) return

      loading.value = true

      try {
        let res
        if (formType.value === 'add') {
          res = await WorkflowDefinitionApi.save(formData)
        } else {
          res = await WorkflowDefinitionApi.update(formData.id, formData)
        }

        if (res.code === 1) {
          ElMessage.success(formType.value === 'add' ? '添加成功' : '编辑成功')

          // 关闭表单
          visible.value = false

          // 触发成功事件
          emit('success')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error(formType.value === 'add' ? '添加失败' : '编辑失败')
      } finally {
        loading.value = false
      }
    })
  }

  // 处理设计跳转
  const handleDesign = () => {
    if (!formData.id) {
      ElMessage.warning('请先保存流程定义')
      return
    }

    router.push({
      path: '/workflow/workflow_definition/designer',
      query: { id: formData.id }
    })

    // 关闭抽屉
    visible.value = false
  }

  // 定义事件
  const emit = defineEmits(['success'])

  // 暴露方法
  defineExpose({
    add: showAddForm,
    edit: showEditForm
  })
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 20px;
  }

  .dialog-footer {
    padding: 10px 20px;
    text-align: right;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-date-editor.el-input),
  :deep(.el-select),
  :deep(.el-cascader),
  :deep(.el-input-number) {
    width: 100%;
  }
</style>

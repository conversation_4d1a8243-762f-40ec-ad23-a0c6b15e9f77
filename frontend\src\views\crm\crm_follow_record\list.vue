<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElDescriptions, ElDescriptionsItem, ElTag, ElTooltip } from 'element-plus'
  import { BgColorEnum } from '@/enums/appEnum'
  import { CrmFollowRecordApi } from '@/api/crm/crmFollowRecord'
  import { ApiStatus } from '@/utils/http/status'
  import { useAuth } from '@/composables/useAuth'

  import { TagColumn } from '@/components/core/tables/columns'

  import FormDialog from './form-dialog.vue'

  // import ImportExportDialog from './import-export-dialog.vue'

  // 权限验证
  const { hasAuth } = useAuth()

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 定义表单搜索初始值
  const initialSearchState = {
    related_type: '',
    follow_type: '',
    next_date: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    /*{
      prop: 'related_type',
      label: '跟进类型',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择跟进类型'
      },
      options: () => [
        { label: '线索跟进', value: 'lead' },
        { label: '商机跟进', value: 'business' },
        { label: '客户跟进', value: 'customer' }
      ],
      onChange: handleFormChange
    },*/
    {
      prop: 'follow_type',
      label: '跟进方式',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择跟进方式'
      },
      options: () => [
        // 跟进方式:phone=电话,visit=拜访,email=邮件,wechat=微信,other=其他
        { label: '电话', value: 'phone' },
        { label: '拜访', value: 'visit' },
        { label: '邮件', value: 'email' },
        { label: '微信', value: 'wechat' },
        { label: '其他', value: 'other' }
      ],
      onChange: handleFormChange
    },
    /*{
      prop: 'follow_date',
      label: '跟进时间',
      type: 'date',
      config: {
        clearable: true,
        placeholder: '请选择跟进时间',
        type: 'date'
      },
      onChange: handleFormChange
    },*/
    {
      prop: 'next_date',
      label: '下次跟进',
      type: 'date',
      config: {
        clearable: true,
        placeholder: '请选择下次跟进时间',
        type: 'date'
      },
      onChange: handleFormChange
    }
  ]

  // 跟进类型选项配置
  const relatedTypeOptions = [
    { value: 'lead', label: '线索跟进', type: 'primary' as const },
    { value: 'business', label: '商机跟进', type: 'success' as const },
    { value: 'customer', label: '客户跟进', type: 'warning' as const }
  ]

  // 跟进方式选项配置
  const followTypeOptions = [
    { value: 'phone', label: '电话', type: 'primary' as const },
    { value: 'visit', label: '拜访', type: 'success' as const },
    { value: 'email', label: '邮件', type: 'info' as const },
    { value: 'wechat', label: '微信', type: 'warning' as const },
    { value: 'other', label: '其他', type: undefined }
  ]

  // 根据跟进类型获取关联对象的标签名称
  const getRelatedLabel = (relatedType: string): string => {
    const labelMap: Record<string, string> = {
      lead: '线索名称',
      business: '商机名称',
      customer: '客户名称'
    }
    return labelMap[relatedType] || '关联对象'
  }

  // 根据跟进类型获取跟进类型的显示文本
  const getRelatedTypeLabel = (relatedType: string): string => {
    const option = relatedTypeOptions.find((item) => item.value === relatedType)
    return option?.label || relatedType
  }

  // 根据跟进方式获取显示文本
  const getFollowTypeLabel = (followType: string): string => {
    const option = followTypeOptions.find((item) => item.value === followType)
    return option?.label || followType
  }

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmFollowRecordApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await CrmFollowRecordApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  //删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmFollowRecordApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } finally {
      loading.value = false
    }
  }

  /*//导入导出对话框引用
  const importExportDialogRef = ref()

  //显示导入对话框
  const showImportDialog = () => {
    importExportDialogRef.value?.showDialog('import')
  }

  //导入导出成功回调
  const handleImportExportSuccess = () => {
    getTableData()
  }

  //显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }*/

  //表单对话框引用
  const formDialogRef = ref()

  //显示表单对话框
  const showFormDialog = (type: string, id?: number) => {
    formDialogRef.value?.showDialog(type, id)
  }

  //表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-crmFollowRecord-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columns="[]" @refresh="handleRefresh">
          <template #left>
            <ElButton v-auth="'crm:crm_follow_record:add'" type="primary" @click="showFormDialog('add')">新增</ElButton>

            <!-- <ElButton type="success" @click="showImportDialog">导入</ElButton>

            <ElButton type="warning" @click="showExportDialog">导出</ElButton> -->
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- 表格列 - 声明式渲染 -->
          <ElTableColumn prop="id" label="ID" width="80" />
          <TagColumn
            prop="related_type"
            label="跟进类型"
            width="120"
            :options="relatedTypeOptions"
          />
          <ElTableColumn prop="related_name" min-width="180">
            <template #header>
              <span>关联对象</span>
            </template>
            <template #default="scope">
              <div>
                <div class="related-label">{{ getRelatedLabel(scope.row.related_type) }}</div>
                <div class="related-name">{{ scope.row.related_name || '-' }}</div>
              </div>
            </template>
          </ElTableColumn>
          <TagColumn prop="follow_type" label="跟进方式" width="120" :options="followTypeOptions" />
          <ElTableColumn prop="last_follow" label="最后跟进" width="280">
            <template #default="scope">
              <div>
                <ElTooltip
                  :content="scope.row.content"
                  :disabled="!scope.row.content || scope.row.content.length <= 50"
                  placement="right"
                  effect="dark"
                  popper-class="follow-tooltip"
                >
                  <div class="follow-content">{{ scope.row.content || '-' }}</div>
                </ElTooltip>
                <div class="follow-time">{{ scope.row.follow_date || '-' }}</div>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="next_follow" label="下次跟进" width="250">
            <template #default="scope">
              <div>
                <ElTooltip
                  :content="scope.row.next_plan"
                  :disabled="!scope.row.next_plan || scope.row.next_plan.length <= 50"
                  placement="right"
                  effect="dark"
                  popper-class="follow-tooltip"
                >
                  <div class="follow-plan">{{ scope.row.next_plan || '-' }}</div>
                </ElTooltip>
                <div class="follow-time">{{ scope.row.next_date || '-' }}</div>
              </div>
            </template>
          </ElTableColumn>
          <!--          <ElTableColumn prop="attachments" label="附件" />-->
          <ElTableColumn prop="creator_name" label="创建人" />
          <ElTableColumn prop="created_at" label="创建时间" width="180" />

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="220">
            <template #default="scope">
              <div>
                <ArtButtonTable v-auth="'crm:crm_follow_record:detail'" text="详情" @click="showDetail(scope.row.id)" />
                <ArtButtonTable
                  v-auth="'crm:crm_follow_record:edit'"
                  text="编辑"
                  :iconClass="BgColorEnum.PRIMARY"
                  @click="showFormDialog('edit', scope.row.id)"
                />
                <ArtButtonTable
                  v-auth="'crm:crm_follow_record:delete'"
                  text="删除"
                  :iconClass="BgColorEnum.DANGER"
                  @click="handleDelete(scope.row.id)"
                />
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="跟进记录表详情"
          width="700px"
          destroy-on-close
          class="detail-dialog"
        >
          <div class="detail-content" style="height: 500px; overflow-y: auto; padding-right: 10px">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="ID">
                {{ detailData.id || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="跟进类型">
                <ElTag
                  :type="
                    relatedTypeOptions.find((item) => item.value === detailData.related_type)?.type
                  "
                >
                  {{ getRelatedTypeLabel(detailData.related_type) }}
                </ElTag>
              </ElDescriptionsItem>

              <ElDescriptionsItem :label="getRelatedLabel(detailData.related_type)">
                {{ detailData.related_name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="跟进方式">
                <ElTag
                  :type="
                    followTypeOptions.find((item) => item.value === detailData.follow_type)?.type
                  "
                >
                  {{ getFollowTypeLabel(detailData.follow_type) }}
                </ElTag>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="跟进内容">
                {{ detailData.content || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="跟进时间">
                {{ detailData.follow_date || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="下次跟进计划">
                {{ detailData.next_plan || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="下次跟进时间">
                {{ detailData.next_date || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="附件">
                {{ detailData.attachments || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建人">
                {{ detailData.creator_name || '-' }}
              </ElDescriptionsItem>

              <!--              <ElDescriptionsItem label="更新人">
                {{ detailData.updated_id || '-' }}
              </ElDescriptionsItem>-->

              <ElDescriptionsItem label="创建时间">
                {{ detailData.created_at || '-' }}
              </ElDescriptionsItem>

              <!--              <ElDescriptionsItem label="更新时间">
                              {{ detailData.updated_at || '-' }}
                            </ElDescriptionsItem>-->
            </ElDescriptions>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 表单组件 -->

        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />

        <!-- 导入导出对话框 -->

        <!-- <ImportExportDialog ref="importExportDialogRef" @success="handleImportExportSuccess" /> -->
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-crmFollowRecord-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    // 关联对象列样式
    .related-label {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-bottom: 2px;
    }

    .related-name {
      font-size: 15px;
      color: var(--art-text-gray-800);
      font-weight: 600;
    }

    // 跟进信息列样式
    .follow-content {
      font-size: 14px;
      color: var(--art-text-gray-800);
      margin-bottom: 4px;
      line-height: 1.4;
      max-height: 2.8em;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .follow-plan {
      font-size: 14px;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
      line-height: 1.4;
      max-height: 2.8em;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .follow-time {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
</style>

<!-- 全局样式，用于tooltip -->
<style lang="scss">
  .follow-tooltip {
    max-width: 300px !important;
    width: 300px !important;
    max-height: 200px !important;

    .el-tooltip__content {
      max-width: 300px !important;
      width: 300px !important;
      max-height: 200px !important;
      overflow-y: auto !important;
      word-wrap: break-word !important;
      white-space: pre-wrap !important;
      line-height: 1.5 !important;
      padding: 12px !important;
    }

    /* 滚动条样式优化 */
    .el-tooltip__content::-webkit-scrollbar {
      width: 6px;
    }

    .el-tooltip__content::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    .el-tooltip__content::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .el-tooltip__content::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }

    /* 详情对话框固定高度样式 */
    :deep(.detail-dialog) {
      .el-dialog__body {
        height: 500px !important;
        padding: 20px !important;
        overflow: hidden !important;
      }

      .detail-content {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
      }

      /* 滚动条样式优化 */
      .detail-content::-webkit-scrollbar {
        width: 6px;
      }

      .detail-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    }
  }
</style>

<script setup lang="ts">
  import { SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { ArticleApi } from '@/api/system/article'
  import { ArticleCategoryApi } from '@/api/system/article_category'
  import { ApiStatus } from '@/utils/http/status'
  import FormDialog from '@/views/system/article/form-dialog.vue'
  import ArticleDetail from '@/components/system/ArticleDetail.vue'

  // 导入需要的列组件
  import SwitchColumn from '@/components/core/tables/columns/SwitchColumn.vue'
  import { BgColorEnum } from '@/enums/appEnum'

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情组件引用
  const articleDetailRef = ref()

  // 定义表单搜索初始值
  const initialSearchState = {
    title: '',
    category_id: null,
    status: null,
    is_top: null,
    is_important: null,
    publish_time: null
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 分类选项
  const categoryOptions = ref<{ label: string; value: number }[]>([])

  // 获取分类选项，并转换为表单选项格式
  const getCategoryOptions = async () => {
    try {
      const res: any = await ArticleCategoryApi.options({})
      if (res.code === ApiStatus.success) {
        categoryOptions.value = res.data || []
      }
    } catch (error) {
      console.error('获取分类选项失败', error)
    }
  }

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      prop: 'title',
      label: '标题',
      type: 'input',
      config: {
        clearable: true
      }
    },
    {
      prop: 'category_id',
      label: '分类',
      type: 'select',
      options: () => categoryOptions.value,
      config: {
        clearable: true
      }
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      options: [
        { value: 0, label: '禁用' },
        { value: 1, label: '启用' }
      ],
      config: {
        clearable: true
      }
    },
    {
      prop: 'is_top',
      label: '是否置顶',
      type: 'select',
      options: [
        { value: 0, label: '否' },
        { value: 1, label: '是' }
      ],
      config: {
        clearable: true
      }
    },
    {
      prop: 'is_important',
      label: '是否重要',
      type: 'select',
      options: [
        { value: 0, label: '否' },
        { value: 1, label: '是' }
      ],
      config: {
        clearable: true
      }
    },
    {
      prop: 'publish_time',
      label: '发布时间',
      type: 'daterange',
      config: {
        clearable: true
      }
    }
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 列显示控制 - 仅保留控制列显示隐藏的功能
  const { columnChecks } = useCheckedColumns(() => [
    { prop: 'id', label: 'ID' },
    { prop: 'title', label: '标题' },
    { prop: 'category_id', label: '分类' },
    { prop: 'sort', label: '排序', sortable: true },
    // { prop: 'summary', label: '摘要' },
    { prop: 'cover_image', label: '封面图片' },
    { prop: 'status', label: '状态', sortable: true },
    { prop: 'is_top', label: '是否置顶', sortable: true },
    { prop: 'is_important', label: '是否重要', sortable: true },
    // { prop: 'view_count', label: '浏览次数', sortable: true },
    { prop: 'attachment', label: '附件' },
    { prop: 'publish_time', label: '发布时间', sortable: true },
    { prop: 'creator_id', label: '创建人' },
    { prop: 'created_at', label: '创建时间', sortable: true },
    { prop: 'operation', label: '操作' }
  ])

  onMounted(() => {
    getCategoryOptions()
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await ArticleApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 获取分类名称
  const getCategoryName = (categoryId: number) => {
    const category = categoryOptions.value.find((item) => item.value === categoryId)
    return category ? category.label : categoryId
  }

  // 显示详情
  const showDetail = async (id: number) => {
    articleDetailRef.value?.showDetail(id)
  }

  // 表单抽屉引用
  const formDialogRef = ref()

  // 显示表单对话框
  const showFormDialog = async (type: string, row?: any) => {
    formDialogRef.value?.showDialog(type, row?.id)
  }

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await ArticleApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } catch {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="system-system_article-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton type="primary" @click="showFormDialog('add')" icon="Plus" v-ripple>
              新增
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          :index="true"
          @size-change="handleSizeChange"
          @page-change="handleCurrentChange"
        >
          <template #empty>
            <div class="empty-container">
              <el-empty :image-size="100" description="暂无数据"></el-empty>
            </div>
          </template>

          <template #default>
            <ElTableColumn prop="category_id" label="分类" width="120">
              <template #default="{ row }">
                {{ getCategoryName(row.category_id) }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="title" label="标题" />
            <editable-column
              label="排序"
              prop="sort"
              width="160"
              :updateApi="ArticleApi.updateField"
              sortable
            />
            <SwitchColumn
              label="状态"
              prop="status"
              :active-value="1"
              :inactive-value="0"
              width="120"
              :updateApi="ArticleApi.updateField"
              sortable
            />
            <SwitchColumn
              label="置顶"
              prop="is_top"
              :active-value="1"
              :inactive-value="0"
              activeText="是"
              inactiveText="否"
              width="120"
              :updateApi="ArticleApi.updateField"
              sortable
            />
            <SwitchColumn
              label="重要"
              prop="is_important"
              :active-value="1"
              :inactive-value="0"
              activeText="是"
              inactiveText="否"
              width="120"
              :updateApi="ArticleApi.updateField"
              sortable
            />
            <!--            <editable-column
                          label="浏览次数"
                          prop="view_count"
                          width="160"
                          :updateApi="ArticleApi.updateField"
                          sortable
                        />-->
            <ElTableColumn prop="publish_time" label="发布时间" width="180" sortable />
            <ElTableColumn prop="created_at" label="创建时间" width="180" sortable />
            <ElTableColumn prop="operation" label="操作" width="240" fixed="right">
              <template #default="{ row }">
                <ArtButtonTable
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(row.id)"
                />
                <ArtButtonTable text="编辑" type="edit" @click="showFormDialog('edit', row)" />
                <ArtButtonTable text="删除" type="delete" @click="handleDelete(row.id)" />
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <!-- 企业资讯详情组件 -->
        <ArticleDetail
          ref="articleDetailRef"
          :category-options="categoryOptions"
          @edit="showFormDialog('edit', $event)"
          @success="handleFormSubmitSuccess"
        />

        <!-- 表单抽屉组件 -->
        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .system-system_article-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    // 文章详情样式
    .article-detail {
      max-height: 58vh;
      display: flex;
      flex-direction: column;

      .article-header {
        margin-bottom: 20px;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 15px;
        flex-shrink: 0; // 防止标题部分被压缩

        .article-title {
          font-size: 24px;
          margin: 0 0 15px;
          color: #303133;
        }

        .article-meta {
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 10px;

          .meta-item {
            margin-right: 20px;
            color: #606266;
            font-size: 13px;
            display: flex;
            align-items: center;

            .el-icon {
              margin-right: 5px;
            }
          }
        }

        .article-tags {
          margin-top: 10px;

          .el-tag {
            margin-right: 8px;
          }
        }
      }

      .article-summary {
        background-color: #f8f9fa;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 3px solid #409eff;
        flex-shrink: 0; // 防止摘要部分被压缩

        .summary-label {
          font-weight: bold;
          margin-bottom: 8px;
          color: #303133;
        }

        .summary-content {
          color: #606266;
          line-height: 1.6;
        }
      }

      .article-scrollable-content {
        flex: 1;
        overflow-y: auto;
        padding-right: 5px; // 为滚动条留出空间
      }

      .article-content {
        margin-bottom: 20px;

        .content-label {
          font-weight: bold;
          margin-bottom: 15px;
          color: #303133;
          padding-left: 10px;
          border-left: 3px solid #409eff;
        }

        .content-body {
          line-height: 1.8;
          color: #303133;

          :deep(img) {
            max-width: 100%;
          }

          :deep(p) {
            margin-bottom: 15px;
          }
        }
      }

      .article-cover {
        margin-bottom: 20px;

        .cover-label {
          font-weight: bold;
          margin-bottom: 10px;
          color: #303133;
        }

        .cover-image {
          .el-image {
            max-width: 400px;
          }
        }
      }

      .article-attachment {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px dashed #ebeef5;

        .attachment-label {
          font-weight: bold;
          margin-bottom: 10px;
          color: #303133;
        }
      }
    }
  }

  /* 优化图片预览器的行为 */
  :deep(.el-image-viewer__wrapper) {
    z-index: 3000 !important;
  }

  :deep(.el-image-viewer__mask) {
    /* 确保遮罩层可以正确响应点击事件 */
    pointer-events: auto !important;
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    /* 防止图片拖动时干扰点击事件 */
    user-select: none;
  }

  :deep(.el-image-viewer__canvas img) {
    /* 确保图片本身不会阻止遮罩层的点击事件 */
    pointer-events: none;
  }
</style>

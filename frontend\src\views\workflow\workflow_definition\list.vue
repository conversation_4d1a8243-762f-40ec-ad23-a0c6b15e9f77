<template>
  <ArtTableFullScreen>
    <div class="workflow_definition-page" id="table-full-screen">
      <ElCard shadow="never" class="workflow_definition-card">
        <!-- 搜索栏 -->
        <ArtSearchBar
          v-model:filter="searchForm"
          :items="searchFormItems"
          @reset="handleReset"
          @search="handleSearch"
        ></ArtSearchBar>

        <ElCard shadow="never" class="art-table-card">
          <!-- 表格头部 -->
          <ArtTableHeader
            :columnList="columnOptions"
            v-model:columns="columnChecks"
            @refresh="handleRefresh"
          >
            <template #left>
              <ElButton @click="handleAdd" icon="Plus" type="primary" v-ripple>新增</ElButton>

              <!--              <ElButton @click="handleDesignNew" icon="Edit" type="success" v-ripple
                              >设计新流程
                            </ElButton>-->

              <!--              <ElButton @click="handleExport" icon="Download" type="success" v-ripple
                              >导出
                            </ElButton>

                            <ElButton @click="handleImport" icon="Upload" v-ripple>导入</ElButton>-->

              <ElButton
                @click="handleBatchDelete"
                type="danger"
                plain
                v-ripple
                icon="Delete"
                :disabled="!selectedKeys.length"
                >批量删除
              </ElButton>
            </template>
          </ArtTableHeader>

          <!-- 表格 -->
          <ArtTable
            :loading="loading"
            :currentPage="currentPage"
            :pageSize="pageSize"
            :data="tableData"
            :total="total"
            :index="true"
            :emptyText="emptyText"
            :marginTop="10"
            :isSelection="true"
            v-model:selectedList="selectedKeys"
            @selection-change="handleSelectionChange"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
            <template #empty>
              <div class="empty-container">
                <el-empty :image-size="100" description="暂无数据"></el-empty>
              </div>
            </template>
            <template #default>
              <ElTableColumn prop="name" label="流程名称" />
              <ElTableColumn prop="type_name" label="流程类型" />
              <ElTableColumn prop="flow_config" label="流程配置">
                <template #default="{ row }">
                  <span class="preview-link" @click="handlePreview(row)">预览</span>
                </template>
              </ElTableColumn>
              <!--              <ElTableColumn prop="icon" label="图标" width="100">
                              <template #default="{ row }">
                                <ImageColumn
                                  label="图标"
                                  :src="row.icon"
                                  :width="60"
                                  :height="60"
                                  :previewable="true"
                                />
                              </template>
                            </ElTableColumn>-->
              <SwitchColumn
                label="状态"
                prop="status"
                :active-value="1"
                :inactive-value="0"
                :updateApi="WorkflowDefinitionApi.updateField"
                fieldName="status"
              />
              <!--              <SwitchColumn
                              label="是否模板"
                              prop="is_template"
                              :active-value="1"
                              :inactive-value="0"
                              :updateApi="WorkflowDefinitionApi.updateField"
                              fieldName="is_template"
                              activeText="是"
                              inactiveText="否"
                            />-->
              <LongTextColumn label="说明" prop="remark" :max-lines="3" />
              <!--              <ElTableColumn prop="form_id" label="表单ID" />-->
              <ElTableColumn prop="created_at" label="创建时间" />
              <ElTableColumn prop="operation" label="操作" width="260">
                <template #default="{ row }">
                  <ArtButtonTable text="编辑" type="edit" @click="handleEdit(row)" />
                  <ArtButtonTable
                    text="配置流程"
                    :iconClass="BgColorEnum.SUCCESS"
                    @click="showDesignForm(row)"
                  />
                  <!--                  <ArtButtonTable text="设计" type="primary" @click="handleDesign(row)" />-->
                  <ArtButtonTable text="删除" type="delete" @click="handleDelete(row)" />
                </template>
              </ElTableColumn>
            </template>
          </ArtTable>
        </ElCard>
      </ElCard>
    </div>

    <!-- 表单抽屉 -->
    <FormDrawer
      ref="formDrawerRef"
      :type-options="typeOptions"
      :types-loading="typesLoading"
      @success="handleSuccess"
    />

    <!-- 表单设计器弹窗 -->
    <ElDialog
      v-model="designFormDialogVisible"
      :destroy-on-close="true"
      top="5vh"
      width="90%"
      :fullscreen="false"
      append-to-body
      :before-close="() => (designFormDialogVisible = false)"
    >
      <div v-loading="designFormLoading" style="height: 80vh; max-height: 800px">
        <!-- 预览模式提示 -->
        <div v-if="workflowReadOnly" class="workflow-preview-tip">
          预览模式：使用键盘方向键(← →)可切换当前显示的节点
        </div>
        <WorkflowDesigner
          ref="workflowRef"
          :workflow-name="workflowName"
          :readOnly="workflowReadOnly"
          :current-executing-node-id="currentExecutingNodeId"
          @save="handleSave"
          @closeClick="designFormDialogVisible = false"
        />
      </div>
    </ElDialog>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { SearchFormItem, SearchSelectOption } from '@/types/search-form'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { WorkflowDefinitionApi } from '@/api/workflow/workflow_definition'
  // import { useRouter } from 'vue-router'
  import type { WorkflowConfig } from '@/components/custom/workflow/types'
  import { BgColorEnum } from '@/enums/appEnum'
  import WorkflowDesigner from '@/components/custom/workflow/index.vue'
  import { deepCopy } from '@/utils/utils' // 导入深拷贝函数

  // 导入表单抽屉组件
  import FormDrawer from './form-drawer.vue'
  import { ApiStatus } from '@/utils/http/status'
  import { WorkflowTypeApi } from '@/api/workflow/workflow_type'

  // 状态与加载
  const loading = ref(false)
  const emptyText = '暂无数据'

  // 表格数据
  const tableData = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 选择的行
  const selectedKeys = ref<number[]>([])

  // 表单抽屉引用
  const formDrawerRef = ref()

  // 搜索表单数据
  const searchForm = reactive({})

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    status: null,
    type_id: null
  }

  // 流程类型选项
  const typesLoading = ref(true) // 添加加载状态
  const typeOptions = ref<SearchSelectOption[]>([])

  // 返回流程类型选项的函数
  const getTypeOptionsFn = (): SearchSelectOption[] => {
    return typeOptions.value || []
  }

  const getFlowTypes = async () => {
    typesLoading.value = true
    try {
      const res = await WorkflowTypeApi.options()
      if (res.code === ApiStatus.success) {
        // 后端已返回正确格式 [{label: "test", value: 1}]
        typeOptions.value = res.data
      }
    } catch (error) {
      console.error('获取流程类型失败:', error)
      // 出错时设置默认选项
      typeOptions.value = [{ value: 0, label: '加载失败' }]
    } finally {
      typesLoading.value = false
    }
  }

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 动态列配置
  const { columnChecks } = useCheckedColumns(() => [
    { prop: 'name', label: '流程名称' },
    { prop: 'type_id', label: '流程类型' },
    {
      prop: 'status',
      label: '状态',
      activeValue: 1,
      inactiveValue: 0
    },
    // {
    //   prop: 'is_template',
    //   label: '是否为模板',
    //   activeValue: 1,
    //   inactiveValue: 0
    // },
    // { prop: 'remark', label: '说明', maxLines: 3, tooltip: true },
    { prop: 'created_at', label: '创建时间' }
  ])

  // 搜索表单项
  const searchFormItems: SearchFormItem[] = [
    {
      prop: 'name',
      label: '流程名称',
      type: 'input',
      config: {
        clearable: true
      }
    },
    {
      prop: 'type_id',
      label: '流程类型',
      type: 'select',
      config: {
        clearable: true
      },
      options: getTypeOptionsFn
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      config: {
        clearable: true
      },
      options: [
        { value: 0, label: '禁用' },
        { value: 1, label: '启用' }
      ]
    }
  ]

  // const router = useRouter()

  onMounted(() => {
    getTableData()
    getFlowTypes()
  })

  // 获取表格数据
  const getTableData = async () => {
    try {
      loading.value = true
      const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        ...searchForm
      }

      const res = await WorkflowDefinitionApi.list(params)

      if (res.code === 1) {
        tableData.value = res.data.list || []
        total.value = res.data.total || 0
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      tableData.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 重置表单
  const handleReset = () => {
    Object.assign(searchForm, { ...initialSearchState })
    currentPage.value = 1
    getTableData()
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 处理表格行选择变化
  const handleSelectionChange = (selection: any[]) => {
    selectedKeys.value = selection.map((item) => item.id)
  }

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 处理新增
  const handleAdd = () => {
    formDrawerRef.value.add()
  }

  // 处理编辑
  const handleEdit = (row: any) => {
    formDrawerRef.value.edit(row.id)
  }

  // 处理设计新流程
  /*const handleDesignNew = () => {
    router.push({
      path: '/workflow/workflow_definition/designer'
    })
  }*/

  // 处理删除
  const handleDelete = (row: any) => {
    ElMessageBox.confirm(`确认删除该数据吗？`, '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await WorkflowDefinitionApi.delete([row.id])

          if (res.code === 1) {
            ElMessage.success('删除成功')
            await getTableData()
          }
        } catch (error) {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (!selectedKeys.value.length) {
      ElMessage.warning('请至少选择一条数据')
      return
    }

    ElMessageBox.confirm(`确认删除选中的 ${selectedKeys.value.length} 条数据吗？`, '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await WorkflowDefinitionApi.delete(selectedKeys.value)

          if (res.code === 1) {
            ElMessage.success('批量删除成功')
            selectedKeys.value = []
            await getTableData()
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          ElMessage.error('批量删除失败')
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }

  // 导出处理方法
  const handleExport = async () => {
    try {
      loading.value = true
      const params = { ...searchForm }
      const res = await WorkflowDefinitionApi.export(params)

      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '工作流程定义表导出数据.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      loading.value = false
    }
  }

  // 导入处理方法
  const handleImport = async () => {
    // 实现文件选择和上传逻辑
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'

    input.onchange = async (e: Event) => {
      const target = e.target as HTMLInputElement
      const file = target.files?.[0]
      if (!file) return

      try {
        loading.value = true
        // API需要File类型参数，不需要FormData
        const res = await WorkflowDefinitionApi.import(file)

        if (res.code === 1) {
          ElMessage.success(`导入成功，成功${res.data.success}条，失败${res.data.fail}条`)
          await getTableData()
        } else {
          ElMessage.error(res.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败')
      } finally {
        loading.value = false
      }
    }

    input.click()
  }

  // 表单提交成功处理
  const handleSuccess = () => {
    getTableData()
  }

  // 定义工作流组件实例的类型
  type WorkflowDesignerInstance = InstanceType<typeof WorkflowDesigner> & {
    loadWorkflowData: (data: any) => void
    resetWorkflow: () => void
    setActiveNode: (node: any) => void
    getWorkflowConfig: () => any
  }

  // 表单设计器对话框
  const designFormDialogVisible = ref(false)
  const designFormLoading = ref(false)

  const workflowId = ref(0),
    workflowName = ref(''),
    workflowRef = ref<WorkflowDesignerInstance | null>(null),
    workflowReadOnly = ref(false),
    currentExecutingNodeId = ref('')

  // 重置工作流设计器状态
  const resetWorkflowDesigner = () => {
    workflowId.value = 0
    workflowName.value = ''
    workflowReadOnly.value = false
    currentExecutingNodeId.value = ''
    // 确保组件引用清理
    workflowRef.value = null
  }

  // 修改预览方法，改用组件方法调用方式设置数据
  const handlePreview = async (row: any) => {
    // 如果对话框已经打开，先关闭它
    if (designFormDialogVisible.value) {
      designFormDialogVisible.value = false
      // 等待对话框完全关闭
      await new Promise((resolve) => setTimeout(resolve, 300))
    }

    // 重置状态
    resetWorkflowDesigner()

    workflowId.value = row.id
    workflowName.value = row.name
    workflowReadOnly.value = true // 设置为只读模式

    // 打开对话框
    designFormDialogVisible.value = true
    designFormLoading.value = true

    // 延迟处理，确保组件已经挂载
    setTimeout(() => {
      try {
        if (row.flow_config && workflowRef.value) {
          // 准备使用的数据 - 深拷贝
          const flowConfigCopy = deepCopy(row.flow_config)

          // 使用组件提供的方法设置数据
          workflowRef.value.loadWorkflowData(flowConfigCopy)

          // 设置预览节点
          const firstNodeId = getFirstNonPromoterNodeId(flowConfigCopy)
          if (firstNodeId) {
            // 组件没有直接修改执行节点的方法，使用currentExecutingNodeId状态
            currentExecutingNodeId.value = firstNodeId
          }

          // 添加键盘事件
          document.addEventListener('keydown', handlePreviewKeydown)
        }
      } catch (error) {
        console.error('加载预览模式出错:', error)
        ElMessage.error('加载预览模式失败')
      } finally {
        designFormLoading.value = false
      }
    }, 300)
  }

  // 处理预览模式下的键盘事件
  const handlePreviewKeydown = (e: KeyboardEvent) => {
    if (!designFormDialogVisible.value || !workflowReadOnly.value) return

    // 如果工作流数据不存在，则退出
    if (!workflowRef.value) return

    // 获取组件中的工作流配置
    const config = workflowRef.value.getWorkflowConfig()
    if (!config) return

    // 获取所有节点ID
    const nodeIds = getAllNodeIds(config.nodeConfig)
    if (nodeIds.length === 0) return

    const currentIndex = nodeIds.indexOf(currentExecutingNodeId.value)

    // 左右箭头切换节点
    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
      // 下一个节点
      const nextIndex = currentIndex >= nodeIds.length - 1 ? 0 : currentIndex + 1
      currentExecutingNodeId.value = nodeIds[nextIndex]
    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
      // 上一个节点
      const prevIndex = currentIndex <= 0 ? nodeIds.length - 1 : currentIndex - 1
      currentExecutingNodeId.value = nodeIds[prevIndex]
    }
  }

  // 获取工作流中所有节点ID
  function getAllNodeIds(flowConfig: any): string[] {
    const nodeIds: string[] = []

    function collectNodeIds(node: any) {
      if (!node) return

      // 添加当前节点ID
      if (node.nodeId) {
        nodeIds.push(node.nodeId)
      }

      // 处理子节点
      if (node.childNode) {
        collectNodeIds(node.childNode)
      }

      // 处理条件分支节点
      if (node.type === 4 && node.conditionNodes) {
        for (const condition of node.conditionNodes) {
          if (condition.nodeId) {
            nodeIds.push(condition.nodeId)
          }
          if (condition.childNode) {
            collectNodeIds(condition.childNode)
          }
        }
      }
    }

    // 从根节点开始收集
    if (flowConfig) {
      collectNodeIds(flowConfig)
    }

    return nodeIds
  }

  // 对话框关闭时移除键盘事件监听
  watch(designFormDialogVisible, (newVal) => {
    if (!newVal) {
      document.removeEventListener('keydown', handlePreviewKeydown)
      // 延迟重置状态，确保组件已完全卸载
      setTimeout(() => {
        resetWorkflowDesigner()
      }, 300)
    }
  })

  const showDesignForm = async (item: any) => {
    // 如果对话框已经打开，先关闭它
    if (designFormDialogVisible.value) {
      designFormDialogVisible.value = false
      // 等待对话框完全关闭
      await new Promise((resolve) => setTimeout(resolve, 300))
    }

    // 重置状态
    resetWorkflowDesigner()

    workflowId.value = item.id
    workflowName.value = item.name
    workflowReadOnly.value = false

    // 打开对话框
    designFormDialogVisible.value = true
    designFormLoading.value = true

    // 使用setTimeout确保数据处理与UI渲染分离
    setTimeout(() => {
      try {
        // 设置数据
        if (item.flow_config && workflowRef.value) {
          // 深拷贝数据
          const configCopy = deepCopy(item.flow_config)

          // 使用组件提供的方法设置数据
          workflowRef.value.loadWorkflowData(configCopy)
        }
      } catch (error) {
        console.error('加载工作流配置出错:', error)
        ElMessage.error('加载工作流配置失败')
      } finally {
        designFormLoading.value = false
      }
    }, 300)
  }

  async function handleSave(workflowConfig: WorkflowConfig) {
    try {
      loading.value = true
      // 深拷贝配置对象，确保不带有Vue的响应式包装
      const configToSave = deepCopy(workflowConfig)
      const res = await WorkflowDefinitionApi.updateField({
        id: workflowId.value,
        field: 'flow_config',
        value: configToSave
      })
      if (res.code === ApiStatus.success) {
        ElMessage.success('保存成功')
      }
    } finally {
      loading.value = false
      designFormDialogVisible.value = false
      await getTableData()
    }
  }

  /**
   * 获取第一个非发起人节点ID（用于演示预览模式下的当前执行节点）
   * @param flowConfig 工作流配置
   */
  function getFirstNonPromoterNodeId(flowConfig: any): string {
    if (!flowConfig || !flowConfig.nodeConfig) {
      return ''
    }

    // 递归查找第一个非发起人节点
    function findFirstNonPromoterNode(node: any): string {
      if (!node) return ''

      // 如果当前节点不是发起人节点，返回其ID
      if (node.type !== 0) {
        return node.nodeId
      }

      // 检查子节点
      if (node.childNode) {
        return findFirstNonPromoterNode(node.childNode)
      }

      // 如果是条件分支节点，检查第一个条件的子节点
      if (node.type === 4 && node.conditionNodes && node.conditionNodes.length > 0) {
        for (const condition of node.conditionNodes) {
          if (condition.nodeId && condition.type !== 0) {
            return condition.nodeId
          }
          if (condition.childNode) {
            const childId = findFirstNonPromoterNode(condition.childNode)
            if (childId) return childId
          }
        }
      }

      return ''
    }

    return findFirstNonPromoterNode(flowConfig.nodeConfig)
  }
</script>

<style lang="scss" scoped>
  .workflow_definition-page {
    .preview-link {
      color: #409eff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .workflow-preview-tip {
      background-color: #ecf8ff;
      padding: 8px 16px;
      color: #409eff;
      border-radius: 4px;
      font-size: 14px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      border-left: 5px solid #409eff;
    }

    .art-table-card {
      margin-top: 15px;
      min-height: 600px;

      :deep(.el-table) {
        min-height: 500px;
      }

      :deep(.el-table__empty-block) {
        min-height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .empty-container {
        width: 100%;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>

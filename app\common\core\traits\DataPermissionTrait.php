<?php
declare(strict_types=1);

namespace app\common\core\traits;

use app\common\utils\DataPermissionCacheUtil;
use think\db\BaseQuery;
use think\facade\Log;

/**
 * 数据权限特性
 * 处理数据权限过滤逻辑，提供高性能的权限检查机制
 */
trait DataPermissionTrait
{
	/**
	 * 数据权限查询性能阈值
	 * 当权限ID数量超过此值时，使用EXISTS子查询替代IN查询
	 */
	protected int $dataPermissionThreshold = 100;
	
	/**
	 * 应用数据权限过滤
	 * 支持双重超级管理员体系：系统超级管理员 + 租户超级管理员
	 *
	 * @param BaseQuery $query 查询构造器
	 * @param string    $field 数据权限字段
	 * @return BaseQuery
	 */
	protected function applyDataPermission(BaseQuery $query, string $field): BaseQuery
	{
		$request            = request();
		$adminId            = $request->adminId ?? 0;
		$isSuperAdmin       = is_super_admin();
		$isTenantSuperAdmin = is_tenant_super_admin();
		
		// 1. 系统超级管理员：根据工作模式决定权限范围
		/* if ($isSuperAdmin) {
			 // 检查是否应该应用租户隔离
			 if (!should_apply_tenant_isolation()) {
				 // 系统管理模式：跳过所有权限检查
				 return $query;
			 }
			 // 租户管理模式：继续应用租户隔离和数据权限
		 }*/
		
		// 获取有效的租户ID（考虑租户切换）
		$tenantId = get_effective_tenant_id();
		
		// 断言：TokenAuthMiddleware已经确保了tenant_id >= 0
		//        assert($tenantId >= 0, 'Effective tenant ID should be >= 0 after TokenAuthMiddleware validation');
		
		// 2. 应用租户隔离（除系统超级管理员外强制应用）
		// 注意：tenant_id=0也是有效租户（系统级租户）
		/*if (method_exists($query->getModel(), 'hasField') &&
			$query->getModel()->hasField('tenant_id')) {
			$query->where('tenant_id', '=', $tenantId);
		}*/
		
		// 3. 数据权限：租户超级管理员在租户范围内有全部权限
		if (!empty($field) && method_exists($query->getModel(), 'hasField') && $query->getModel()
		                                                                             ->hasField($field)) {
			
			// 租户超级管理员在租户范围内有全部数据权限
			if ($isTenantSuperAdmin || $isSuperAdmin) {
				return $query;
			}
			
			// 普通用户应用数据权限
			if ($adminId <= 0) {
				// 无效用户ID，拒绝所有访问
				$query->where('1', '=', '0');
				return $query;
			}
			
			try {
				// 获取数据权限范围
				$adminIds = DataPermissionCacheUtil::getUserDataPermission($adminId, $tenantId);
				
				if (!empty($adminIds)) {
					if (count($adminIds) > $this->dataPermissionThreshold) {
						// 权限ID数量较多，使用EXISTS子查询优化性能
						$this->applyDataPermissionWithExists($query, $field, $adminId, $tenantId);
					}
					else {
						// 权限ID数量较少，使用IN查询
						$query->whereIn($field, $adminIds);
					}
				}
			}
			catch (\Exception $e) {
				// 权限检查失败，记录日志并限制为只能访问自己的数据
				Log::error('数据权限检查失败', [
					'admin_id'              => $adminId,
					'tenant_id'             => $tenantId,
					'field'                 => $field,
					'is_super_admin'        => $isSuperAdmin,
					'is_tenant_super_admin' => $isTenantSuperAdmin,
					'error'                 => $e->getMessage(),
					'trace'                 => $e->getTraceAsString()
				]);
				$query->where($field, '=', $adminId);
			}
		}
		
		return $query;
	}
	
	/**
	 * 使用EXISTS子查询应用数据权限
	 * 适用于权限ID数量较多的场景，避免大量IN查询的性能问题
	 *
	 * @param BaseQuery $query    查询构造器
	 * @param string    $field    数据权限字段
	 * @param int       $adminId  管理员ID
	 * @param int       $tenantId 租户ID
	 * @return void
	 */
	protected function applyDataPermissionWithExists(BaseQuery $query, string $field, int $adminId, int $tenantId): void
	{
		$query->whereExists(function ($subQuery) use ($adminId, $tenantId, $field) {
			$subQuery->table('system_admin_role as sar')
			         ->join('system_role as sr', 'sar.role_id = sr.id')
			         ->join('system_dept as sd', 'sr.data_scope_dept_ids LIKE CONCAT("%", sd.id, "%") OR sr.data_scope IN (1,2,3)')
			         ->join('system_admin as sa', 'sa.dept_id = sd.id OR sa.id = ' . $adminId)
			         ->where('sar.admin_id', $adminId)
			         ->where('sar.tenant_id', $tenantId)
			         ->where('sr.status', 1)
			         ->whereColumn('sa.id', $field)
			         ->limit(1);
		});
	}
	
	/**
	 * 获取数据权限范围内的ID列表
	 *
	 * @return array
	 */
	protected function getDataPermissionIds(): array
	{
		// 超级管理员不受数据权限限制
		if (is_super_admin()) {
			return [];
		}
		
		$request  = request();
		$adminId  = $request->adminId ?? 0;
		$tenantId = $request->tenantId ?? 0;
		
		if ($adminId <= 0) {
			return [];
		}
		
		try {
			return DataPermissionCacheUtil::getUserDataPermission($adminId, $tenantId)
				?: [];
		}
		catch (\Exception $e) {
			Log::error('获取数据权限ID列表失败', [
				'admin_id'  => $adminId,
				'tenant_id' => $tenantId,
				'error'     => $e->getMessage()
			]);
			return [$adminId]; // 失败时只返回自己的ID
		}
	}
	
	/**
	 * 设置数据权限查询性能阈值
	 *
	 * @param int $threshold 阈值
	 * @return $this
	 */
	public function setDataPermissionThreshold(int $threshold): self
	{
		$this->dataPermissionThreshold = max(1, $threshold);
		return $this;
	}
	
	/**
	 * 获取数据权限查询性能阈值
	 *
	 * @return int
	 */
	public function getDataPermissionThreshold(): int
	{
		return $this->dataPermissionThreshold;
	}
}
<template>
  <!-- 详情对话框 -->
  <ElDialog v-model="visible" width="60%" destroy-on-close>
    <div class="article-detail" v-loading="loading">
      <!-- 固定的标题和摘要部分 -->
      <div class="article-header">
        <h2 class="article-title">{{ detailData.title }}</h2>
        <div class="article-meta">
          <span class="meta-item">
            <el-icon><Calendar /></el-icon>
            发布时间：{{ detailData.publish_time || '未设置' }}
          </span>
          <span class="meta-item">
            <el-icon><Collection /></el-icon>
            分类：{{ getCategoryName(detailData.category_id) }}
          </span>
          <span class="meta-item">
            <el-icon><View /></el-icon>
            阅读：{{ detailData.view_count || 0 }}
          </span>
        </div>
        <div class="article-tags">
          <el-tag v-if="detailData.status === 1" type="success" effect="light">已发布</el-tag>
          <el-tag v-else type="info" effect="light">未发布</el-tag>

          <el-tag v-if="detailData.is_top === 1" type="warning" effect="light">置顶</el-tag>
          <el-tag v-if="detailData.is_important === 1" type="danger" effect="light">重要</el-tag>
        </div>
      </div>

      <!-- 可滚动的内容部分 -->
      <div class="article-scrollable-content">
        <div class="article-summary" v-if="detailData.summary">
          <div class="summary-label">摘要：</div>
          <div class="summary-content">{{ detailData.summary }}</div>
        </div>

        <div class="article-content">
          <div class="content-body" v-html="detailData.content"></div>
        </div>

        <div class="article-cover" v-if="detailData.cover_image">
          <div class="cover-label">封面图片：</div>
          <div class="cover-image">
            <el-image
              :src="detailData.cover_image"
              fit="cover"
              :preview-src-list="[detailData.cover_image]"
              :preview-teleported="true"
              :initial-index="0"
              :hide-on-click-modal="true"
              :z-index="3000"
              :infinite="false"
              :zoom-rate="1.2"
              :min-scale="0.2"
              :max-scale="7"
            />
          </div>
        </div>

        <div class="article-attachment" v-if="detailData.attachment">
          <div class="attachment-label">附件：</div>
          <div class="attachment-content">
            <el-button type="primary" link @click="openAttachment(detailData.attachment)">
              <el-icon>
                <Document />
              </el-icon>
              查看/下载附件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="visible = false">关闭</ElButton>
        <ElButton v-if="showActions" type="primary" @click="handleEdit">编辑文章</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage, ElDialog, ElButton } from 'element-plus'
  import { Calendar, Collection, View, Document } from '@element-plus/icons-vue'
  import { ArticleApi } from '@/api/system/article'
  import { ApiStatus } from '@/utils/http/status'

  interface Props {
    showActions?: boolean // 是否显示操作按钮
    categoryOptions?: Array<{ label: string; value: number }> // 分类选项
  }

  const props = withDefaults(defineProps<Props>(), {
    showActions: true,
    categoryOptions: () => []
  })

  const emit = defineEmits<{
    edit: [data: any]
    success: []
  }>()

  const visible = ref(false)
  const loading = ref(false)
  const detailData = ref<any>({})

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      visible.value = true
      const res = await ArticleApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
      } else {
        ElMessage.error(res.message || '获取详情失败')
        visible.value = false
      }
    } finally {
      loading.value = false
    }
  }

  // 获取分类名称
  const getCategoryName = (categoryId: number) => {
    const category = props.categoryOptions.find((item) => item.value === categoryId)
    return category ? category.label : categoryId
  }

  // 打开附件
  const openAttachment = (url: string) => {
    if (url) {
      window.open(url)
    }
  }

  // 处理编辑
  const handleEdit = () => {
    emit('edit', detailData.value)
  }

  // 暴露方法
  defineExpose({
    showDetail
  })
</script>

<style scoped lang="scss">
  .article-detail {
    .article-header {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      .article-title {
        margin: 0 0 15px 0;
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        line-height: 1.4;
      }

      .article-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 15px;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 14px;
          color: #606266;

          .el-icon {
            font-size: 16px;
          }
        }
      }

      .article-tags {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }

    .article-scrollable-content {
      max-height: 45vh;
      overflow-y: auto;
      padding-right: 10px;

      .article-summary {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 6px;

        .summary-label {
          font-weight: bold;
          margin-bottom: 10px;
          color: #303133;
        }

        .summary-content {
          color: #606266;
          line-height: 1.6;
        }
      }

      .article-content {
        margin-bottom: 20px;

        .content-body {
          line-height: 1.8;
          color: #303133;
          word-break: break-word;

          :deep(img) {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
          }

          :deep(p) {
            margin-bottom: 15px;
          }

          :deep(h1),
          :deep(h2),
          :deep(h3),
          :deep(h4),
          :deep(h5),
          :deep(h6) {
            margin: 20px 0 15px 0;
            color: #303133;
          }

          :deep(ul),
          :deep(ol) {
            padding-left: 20px;
            margin-bottom: 15px;
          }

          :deep(blockquote) {
            border-left: 4px solid #409eff;
            padding-left: 15px;
            margin: 15px 0;
            color: #606266;
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
          }
        }
      }

      .article-cover {
        margin-bottom: 20px;

        .cover-label {
          font-weight: bold;
          margin-bottom: 10px;
          color: #303133;
        }

        .cover-image {
          .el-image {
            max-width: 400px;
          }
        }
      }

      .article-attachment {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px dashed #ebeef5;

        .attachment-label {
          font-weight: bold;
          margin-bottom: 10px;
          color: #303133;
        }
      }
    }
  }

  /* 优化图片预览器的行为 */
  :deep(.el-image-viewer__wrapper) {
    z-index: 3000 !important;
  }

  :deep(.el-image-viewer__mask) {
    pointer-events: auto !important;
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    user-select: none;
  }

  :deep(.el-image-viewer__canvas img) {
    pointer-events: none;
  }
</style>

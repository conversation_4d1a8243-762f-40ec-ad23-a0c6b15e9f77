# 办公审批系统管理员手册

## 📋 文档概述

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**适用范围：** 系统管理员、租户管理员  
**系统版本：** Base Admin v2.0

## 🎯 管理员职责

### 系统超级管理员
- 管理所有租户的工作流配置
- 处理跨租户的审批问题
- 系统维护和故障处理
- 全局权限和配置管理

### 租户超级管理员
- 管理租户内的工作流配置
- 处理租户内的审批异常
- 用户权限分配和管理
- 租户级别的系统配置

## 🏗️ 系统架构

### 核心组件
```
办公审批系统
├── 工作流引擎          # 核心审批流程引擎
├── 权限管理系统        # 用户权限和数据权限控制
├── 消息通知系统        # 审批消息推送
├── 表单管理系统        # 动态表单配置
└── 审计日志系统        # 操作记录和审计
```

### 数据库表结构
| 表名 | 说明 |
|------|------|
| workflow_definition | 工作流定义表 |
| workflow_instance | 工作流实例表 |
| workflow_task | 工作流任务表 |
| workflow_history | 工作流历史记录表 |
| workflow_type | 工作流类型配置表 |

## ⚙️ 工作流配置管理

### 1. 工作流定义管理

#### 创建工作流定义
1. 进入"系统管理" → "工作流管理" → "流程定义"
2. 点击"新增"按钮
3. 填写基本信息：
   - 流程名称
   - 流程描述
   - 业务类型
   - 表单配置
4. 设计审批流程：
   - 添加审批节点
   - 配置审批人
   - 设置流程条件
   - 配置抄送规则

#### 审批节点类型
| 节点类型 | 说明 | 配置要点 |
|----------|------|----------|
| 开始节点 | 流程起始点 | 无需配置 |
| 审批节点 | 需要人工审批 | 配置审批人、审批方式 |
| 抄送节点 | 信息抄送 | 配置抄送人员 |
| 条件节点 | 流程分支 | 配置分支条件 |
| 结束节点 | 流程终点 | 无需配置 |

#### 审批人设置方式
| 设置方式 | 说明 | 适用场景 |
|----------|------|----------|
| 指定成员 | 直接指定具体用户 | 固定审批人 |
| 部门主管 | 根据申请人部门自动确定 | 层级审批 |
| 发起人自选 | 申请时由发起人选择 | 灵活审批 |
| 角色审批 | 指定角色的用户审批 | 职能审批 |
| 表单人员 | 从表单字段获取审批人 | 动态审批 |

### 2. 工作流类型管理

#### 配置业务类型
```sql
-- 工作流类型配置示例
INSERT INTO workflow_type (name, module_code, business_code, status) VALUES
('请假申请', 'hr', 'hr_leave', 1),
('合同审批', 'crm', 'crm_contract', 1),
('采购申请', 'purchase', 'purchase_order', 1),
('报销申请', 'finance', 'expense_report', 1);
```

#### 业务表字段要求
```sql
-- 业务表必需字段
approval_status INT DEFAULT 0 COMMENT '审批状态',
workflow_instance_id INT DEFAULT NULL COMMENT '工作流实例ID',
submit_time DATETIME DEFAULT NULL COMMENT '提交时间',
approval_time DATETIME DEFAULT NULL COMMENT '审批完成时间',
creator_id INT DEFAULT 0 COMMENT '创建人ID',
tenant_id INT DEFAULT 0 COMMENT '租户ID'
```

## 👥 权限管理

### 权限层级设计

#### 1. 菜单权限
```
办公审批 (workflow:index)
├── 我的申请 (workflow:application:index)
├── 我的审批 (workflow:task:index)
├── 我的抄送 (workflow:cc:index)
└── 流程管理 (workflow:definition:index) [管理员专用]
```

#### 2. 数据权限配置
| 权限级别 | 数据范围 | 适用角色 |
|----------|----------|----------|
| 全部数据 | 租户内所有数据 | 租户管理员 |
| 本部门及以下 | 本部门和下级部门 | 部门主管 |
| 本部门 | 仅本部门数据 | 部门经理 |
| 仅本人 | 只能看自己的数据 | 普通员工 |
| 自定义 | 指定部门范围 | 特殊角色 |

#### 3. 操作权限控制
```php
// 审批操作权限检查
if ($task['approver_id'] != $this->request->adminId && !$this->hasAdminPermission()) {
    return $this->error('无权操作此任务');
}
```

### 权限分配指南

#### 1. 普通员工权限
- 查看：自己的申请和审批任务
- 操作：提交申请、处理分配给自己的审批

#### 2. 部门主管权限
- 查看：本部门的申请和审批（数据权限）
- 操作：只能处理分配给自己的审批任务（业务权限）

#### 3. 管理员权限
- 查看：全部或指定范围的数据
- 操作：可以处理任何审批任务
- 管理：配置工作流、分配权限

## 📊 监控和维护

### 1. 系统监控指标

#### 业务指标
- 申请提交成功率 (目标: >99%)
- 审批处理及时率 (目标: >95%)
- 流程完成率 (目标: >98%)
- 用户满意度 (目标: >90%)

#### 技术指标
- 系统响应时间 (目标: <3秒)
- 数据库连接池使用率 (目标: <80%)
- 错误日志数量 (目标: <10/天)
- 并发处理能力 (目标: >100/秒)

### 2. 日常维护任务

#### 每日检查
- [ ] 检查系统错误日志
- [ ] 监控审批任务处理情况
- [ ] 查看用户反馈和问题
- [ ] 检查系统性能指标

#### 每周维护
- [ ] 清理过期的临时数据
- [ ] 备份重要配置数据
- [ ] 分析系统使用统计
- [ ] 更新用户权限配置

#### 每月优化
- [ ] 分析流程效率报告
- [ ] 优化慢查询和性能瓶颈
- [ ] 更新系统文档
- [ ] 用户培训和反馈收集

### 3. 故障处理

#### 常见问题及解决方案

**问题1：审批任务创建失败**
```bash
# 检查错误日志
tail -f runtime/log/error.log

# 检查数据库连接
php think test:database

# 重启相关服务
systemctl restart nginx php-fpm mysql
```

**问题2：权限验证失败**
```php
// 清理权限缓存
Cache::tag('menu')->clear();
Cache::tag('permission')->clear();

// 重新加载权限数据
php think permission:reload
```

**问题3：工作流引擎异常**
```sql
-- 检查工作流实例状态
SELECT * FROM workflow_instance WHERE status = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 检查待处理任务
SELECT * FROM workflow_task WHERE status = 0 AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
```

## 🔧 系统配置

### 1. 基础配置

#### 工作流引擎配置
```php
// config/workflow.php
return [
    'engine' => [
        'timeout' => 300,           // 引擎超时时间
        'max_retry' => 3,           // 最大重试次数
        'enable_cache' => true,     // 启用缓存
        'cache_expire' => 3600,     // 缓存过期时间
    ],
    'notification' => [
        'enable' => true,           // 启用通知
        'channels' => ['database', 'email'], // 通知渠道
        'templates' => [
            'approval_pending' => '您有新的审批任务',
            'approval_approved' => '您的申请已通过',
            'approval_rejected' => '您的申请已驳回',
        ]
    ]
];
```

#### 权限配置
```php
// config/permission.php
return [
    'data_permission' => [
        'enable' => true,           // 启用数据权限
        'cache_expire' => 3600,     // 缓存过期时间
        'default_scope' => 4,       // 默认权限范围（仅本人）
    ],
    'menu_permission' => [
        'enable' => true,           // 启用菜单权限
        'cache_expire' => 7200,     // 缓存过期时间
        'strict_mode' => true,      // 严格模式
    ]
];
```

### 2. 性能优化配置

#### 数据库优化
```sql
-- 添加必要的索引
ALTER TABLE workflow_instance ADD INDEX idx_submitter_status (submitter_id, status);
ALTER TABLE workflow_task ADD INDEX idx_approver_status (approver_id, status);
ALTER TABLE workflow_history ADD INDEX idx_instance_time (instance_id, operation_time);
```

#### 缓存配置
```php
// config/cache.php
return [
    'stores' => [
        'workflow' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 2,
            'timeout' => 0,
            'expire' => 3600,
            'persistent' => false,
            'prefix' => 'workflow:',
        ]
    ]
];
```

## 📈 报表和统计

### 1. 业务报表

#### 审批效率报表
- 平均审批时长
- 各部门审批效率
- 审批通过率统计
- 流程瓶颈分析

#### 用户活跃度报表
- 用户申请频次
- 审批处理及时性
- 系统使用情况
- 功能使用统计

### 2. 系统报表

#### 性能监控报表
- 系统响应时间趋势
- 数据库查询性能
- 缓存命中率
- 错误率统计

#### 容量规划报表
- 数据增长趋势
- 存储空间使用
- 并发用户数
- 系统负载情况

## 🚨 应急处理

### 1. 紧急故障处理流程

#### 故障等级定义
| 等级 | 影响范围 | 响应时间 | 处理时间 |
|------|----------|----------|----------|
| P0 | 系统完全不可用 | 15分钟 | 2小时 |
| P1 | 核心功能不可用 | 30分钟 | 4小时 |
| P2 | 部分功能异常 | 1小时 | 8小时 |
| P3 | 轻微问题 | 4小时 | 24小时 |

#### 应急联系方式
- **技术负责人**：[联系方式]
- **系统管理员**：[联系方式]
- **业务负责人**：[联系方式]

### 2. 数据恢复预案

#### 备份策略
- 数据库：每日全量备份 + 实时增量备份
- 配置文件：每周备份
- 日志文件：保留30天

#### 恢复流程
1. 评估数据损失范围
2. 选择合适的备份点
3. 停止相关服务
4. 执行数据恢复
5. 验证数据完整性
6. 重启服务并测试

## 📞 技术支持

### 联系方式
- **技术支持邮箱**：<EMAIL>
- **紧急联系电话**：[电话号码]
- **在线支持**：[支持平台地址]

### 支持时间
- **工作日**：9:00-18:00
- **紧急故障**：7×24小时
- **系统维护**：每周日 2:00-6:00

---

**文档更新日期**：2025-01-24  
**版权所有**：Base Admin 系统

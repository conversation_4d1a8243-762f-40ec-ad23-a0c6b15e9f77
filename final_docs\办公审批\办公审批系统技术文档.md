# 办公审批系统技术文档

## 📋 文档概述

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**适用范围：** 开发人员、技术架构师  
**技术栈：** ThinkPHP8 + MySQL + Redis + Vue3

## 🏗️ 系统架构

### 整体架构图
```mermaid
graph TD
    A[前端 Vue3] --> B[API网关]
    B --> C[业务控制器层]
    C --> D[业务服务层]
    D --> E[工作流引擎]
    E --> F[数据访问层]
    F --> G[MySQL数据库]
    
    D --> H[权限服务]
    H --> I[Redis缓存]
    
    E --> J[消息服务]
    J --> K[消息队列]
```

### 核心模块
| 模块 | 职责 | 技术实现 |
|------|------|----------|
| **工作流引擎** | 流程控制和任务分配 | WorkflowEngineService |
| **权限管理** | 用户权限和数据权限 | PermissionService + DataPermissionTrait |
| **表单管理** | 动态表单配置 | FormServiceInterface |
| **消息通知** | 审批消息推送 | NotificationService |
| **审计日志** | 操作记录追踪 | WorkflowHistoryService |

## 💾 数据库设计

### 核心表结构

#### 1. workflow_definition (工作流定义表)
```sql
CREATE TABLE `workflow_definition` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '流程名称',
  `description` text COMMENT '流程描述',
  `process_config` longtext COMMENT '流程配置JSON',
  `form_config` longtext COMMENT '表单配置JSON',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `business_code` varchar(50) DEFAULT NULL COMMENT '业务代码',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `tenant_id` int(11) DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_business_code` (`business_code`),
  KEY `idx_tenant_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2. workflow_instance (工作流实例表)
```sql
CREATE TABLE `workflow_instance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '申请标题',
  `process_id` int(11) NOT NULL COMMENT '流程定义ID',
  `definition_id` int(11) DEFAULT NULL COMMENT '流程定义ID',
  `business_code` varchar(50) DEFAULT NULL COMMENT '业务代码',
  `business_id` int(11) DEFAULT NULL COMMENT '业务数据ID',
  `submitter_id` int(11) NOT NULL COMMENT '提交人ID',
  `submitter_dept_id` int(11) DEFAULT NULL COMMENT '提交人部门ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0草稿 1审批中 2已通过 3已驳回 4已终止 5已撤回',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `form_data` longtext COMMENT '表单数据JSON',
  `current_node_id` varchar(50) DEFAULT NULL COMMENT '当前节点ID',
  `node_config` longtext COMMENT '节点配置JSON',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `tenant_id` int(11) DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_submitter_status` (`submitter_id`, `status`),
  KEY `idx_business` (`business_code`, `business_id`),
  KEY `idx_tenant_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3. workflow_task (工作流任务表)
```sql
CREATE TABLE `workflow_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` varchar(100) NOT NULL COMMENT '任务唯一标识',
  `instance_id` int(11) NOT NULL COMMENT '流程实例ID',
  `process_id` int(11) NOT NULL COMMENT '流程定义ID',
  `node_id` varchar(50) NOT NULL COMMENT '节点ID',
  `node_name` varchar(100) NOT NULL COMMENT '节点名称',
  `node_type` varchar(20) DEFAULT 'approval' COMMENT '节点类型',
  `task_type` tinyint(1) DEFAULT 1 COMMENT '任务类型：1审批 2抄送',
  `approver_id` int(11) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) DEFAULT NULL COMMENT '审批人姓名',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0待处理 1已通过 2已驳回 3已转交',
  `opinion` text COMMENT '审批意见',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `tenant_id` int(11) DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_approver_status` (`approver_id`, `status`),
  KEY `idx_instance_node` (`instance_id`, `node_id`),
  KEY `idx_tenant_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 4. workflow_history (工作流历史表)
```sql
CREATE TABLE `workflow_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `instance_id` int(11) NOT NULL COMMENT '流程实例ID',
  `process_id` int(11) NOT NULL COMMENT '流程定义ID',
  `task_id` varchar(100) DEFAULT NULL COMMENT '任务ID',
  `node_id` varchar(50) DEFAULT NULL COMMENT '节点ID',
  `node_name` varchar(100) DEFAULT NULL COMMENT '节点名称',
  `node_type` varchar(20) DEFAULT NULL COMMENT '节点类型',
  `prev_node_id` varchar(50) DEFAULT NULL COMMENT '上一节点ID',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `operation` varchar(20) NOT NULL COMMENT '操作类型',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `opinion` text COMMENT '操作意见',
  `created_at` datetime DEFAULT NULL,
  `tenant_id` int(11) DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_instance_time` (`instance_id`, `operation_time`),
  KEY `idx_operator_time` (`operator_id`, `operation_time`),
  KEY `idx_tenant_time` (`tenant_id`, `operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 5. workflow_type (工作流类型表)
```sql
CREATE TABLE `workflow_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '类型名称',
  `module_code` varchar(50) NOT NULL COMMENT '模块代码',
  `business_code` varchar(50) NOT NULL COMMENT '业务代码',
  `description` text COMMENT '描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `updated_id` int(11) DEFAULT NULL COMMENT '更新人ID',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `tenant_id` int(11) DEFAULT 0 COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_code` (`business_code`),
  KEY `idx_module_status` (`module_code`, `status`),
  KEY `idx_tenant_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 索引优化策略

#### 1. 查询优化索引
```sql
-- 我的申请查询优化
ALTER TABLE workflow_instance ADD INDEX idx_submitter_status_time (submitter_id, status, created_at);

-- 我的审批查询优化
ALTER TABLE workflow_task ADD INDEX idx_approver_status_time (approver_id, status, created_at);

-- 审批历史查询优化
ALTER TABLE workflow_history ADD INDEX idx_instance_operation_time (instance_id, operation_time DESC);
```

#### 2. 数据权限查询优化
```sql
-- 部门数据权限查询优化
ALTER TABLE workflow_instance ADD INDEX idx_dept_status (submitter_dept_id, status);

-- 租户隔离查询优化
ALTER TABLE workflow_instance ADD INDEX idx_tenant_status_time (tenant_id, status, created_at);
```

## 🔧 核心服务实现

### 1. 工作流引擎服务

#### WorkflowEngineService
```php
class WorkflowEngineService
{
    /**
     * 启动工作流
     */
    public function startWorkflow(array $instance): bool
    {
        try {
            // 1. 解析流程定义
            $definition = $this->getProcessDefinition($instance['process_id']);
            
            // 2. 获取开始节点
            $startNode = $this->getStartNode($definition['process_config']);
            
            // 3. 处理第一个节点
            return $this->processNode($instance, $startNode);
        } catch (\Exception $e) {
            Log::error('工作流启动失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 处理节点
     */
    private function processNode(array $instance, array $node): bool
    {
        $handler = $this->getNodeHandler($node['nodeType']);
        return $handler->handle($instance, $node, []);
    }
    
    /**
     * 获取节点处理器
     */
    private function getNodeHandler(string $nodeType): NodeHandlerInterface
    {
        $handlers = [
            'approval' => ApprovalNodeHandler::class,
            'cc' => CcNodeHandler::class,
            'condition' => ConditionNodeHandler::class,
            'start' => StartNodeHandler::class,
            'end' => EndNodeHandler::class,
        ];
        
        if (!isset($handlers[$nodeType])) {
            throw new \Exception("不支持的节点类型: {$nodeType}");
        }
        
        return new $handlers[$nodeType]();
    }
}
```

#### 审批节点处理器
```php
class ApprovalNodeHandler implements NodeHandlerInterface
{
    public function handle(array $instance, array $node, array $context): bool
    {
        // 1. 获取审批人列表
        $approvers = $this->getApprovers($instance, $node);
        
        if (empty($approvers)) {
            Log::warning('未找到审批人，跳过节点: ' . $node['nodeName']);
            return $this->handleNextNode($instance, $node['childNode'], $context);
        }
        
        // 2. 创建审批任务
        return $this->createApprovalTasks($instance, $node, $approvers);
    }
    
    /**
     * 获取审批人
     */
    private function getApprovers(array $instance, array $node): array
    {
        $setType = intval($node['setType'] ?? 1);
        
        switch ($setType) {
            case 1: // 指定成员
                return $node['nodeUserList'] ?? [];
            case 4: // 部门主管
                return $this->getDirectorApprovers($instance, $node);
            case 8: // 角色
                return $this->getRoleApprovers($node);
            default:
                return $node['nodeUserList'] ?? [];
        }
    }
}
```

### 2. 权限管理服务

#### 数据权限控制
```php
trait DataPermissionTrait
{
    /**
     * 应用数据权限
     */
    protected function applyDataPermission(BaseQuery $query, string $field): BaseQuery
    {
        $adminId = request()->adminId ?? 0;
        $tenantId = get_tenant_id();
        
        // 超级管理员跳过数据权限
        if (is_super_admin() || is_tenant_super_admin()) {
            return $query;
        }
        
        try {
            // 获取用户数据权限范围
            $adminIds = DataPermissionCacheUtil::getUserDataPermission($adminId, $tenantId);
            
            if (empty($adminIds)) {
                // 没有权限范围，只能访问自己的数据
                $query->where($field, '=', $adminId);
            } else {
                // 有权限范围，使用IN查询
                $query->whereIn($field, $adminIds);
            }
        } catch (\Exception $e) {
            Log::error('数据权限应用失败: ' . $e->getMessage());
            // 异常情况下只能访问自己的数据
            $query->where($field, '=', $adminId);
        }
        
        return $query;
    }
}
```

#### 权限验证中间件
```php
class PermissionMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $adminId = $request->adminId ?? 0;
        
        // 系统超级管理员直接跳过权限检查
        if (is_super_admin()) {
            return $next($request);
        }
        
        // 解析权限标识
        $permission = $this->parsePermission($request);
        
        // 获取用户权限列表
        $userPermissions = $this->getUserPermissions($adminId);
        
        // 验证权限
        if (!in_array($permission, $userPermissions)) {
            throw new PermissionException('没有访问权限');
        }
        
        return $next($request);
    }
}
```

### 3. 表单管理服务

#### FormServiceInterface
```php
interface FormServiceInterface
{
    /**
     * 获取表单配置
     */
    public function getFormConfig(): array;
    
    /**
     * 验证表单数据
     */
    public function validateFormData(array $data): bool;
    
    /**
     * 保存表单数据
     */
    public function saveFormData(array $data): int;
    
    /**
     * 获取审批标题
     */
    public function getApprovalTitle(array $data): string;
    
    /**
     * 审批通过回调
     */
    public function onApprovalApproved(int $businessId, array $instance): bool;
    
    /**
     * 审批驳回回调
     */
    public function onApprovalRejected(int $businessId, array $instance): bool;
    
    /**
     * 审批撤回回调
     */
    public function onApprovalWithdrawn(int $businessId, array $instance): bool;
}
```

## 🚀 API接口设计

### 1. 我的申请接口

#### 获取申请列表
```http
GET /api/workflow/application/index
```

**请求参数：**
```json
{
  "page": 1,
  "limit": 20,
  "status": 1,
  "business_code": "hr_leave",
  "start_time": "2025-01-01",
  "end_time": "2025-01-31"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "请假申请",
        "business_code": "hr_leave",
        "status": 1,
        "status_text": "审批中",
        "submitter_name": "张三",
        "start_time": "2025-01-24 10:00:00",
        "current_approver": "李四"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

#### 提交申请
```http
POST /api/workflow/application/submit
```

**请求参数：**
```json
{
  "business_code": "hr_leave",
  "title": "请假申请",
  "form_data": {
    "leave_type": "年假",
    "start_date": "2025-01-25",
    "end_date": "2025-01-26",
    "reason": "个人事务"
  }
}
```

### 2. 我的审批接口

#### 获取审批任务列表
```http
GET /api/workflow/task/index
```

#### 审批通过
```http
POST /api/workflow/task/approve
```

**请求参数：**
```json
{
  "task_id": 123,
  "opinion": "同意申请"
}
```

#### 审批驳回
```http
POST /api/workflow/task/reject
```

**请求参数：**
```json
{
  "task_id": 123,
  "opinion": "申请理由不充分"
}
```

## 🔍 性能优化

### 1. 数据库优化

#### 查询优化
```php
// 优化前：N+1查询问题
foreach ($instances as $instance) {
    $instance->submitter; // 每次都查询数据库
}

// 优化后：预加载关联数据
$instances = WorkflowInstance::with(['submitter', 'definition'])->get();
```

#### 索引优化
```sql
-- 复合索引优化查询
CREATE INDEX idx_workflow_query ON workflow_instance (tenant_id, submitter_id, status, created_at);

-- 覆盖索引减少回表
CREATE INDEX idx_workflow_cover ON workflow_instance (submitter_id, status, id, title, created_at);
```

### 2. 缓存策略

#### Redis缓存配置
```php
// 权限数据缓存
Cache::tag('permission')->set("user_permissions:{$userId}", $permissions, 3600);

// 工作流定义缓存
Cache::tag('workflow')->set("definition:{$processId}", $definition, 7200);

// 部门数据缓存
Cache::tag('department')->set("dept_users:{$deptId}", $userIds, 1800);
```

#### 缓存更新策略
```php
// 权限变更时清理相关缓存
public function updateUserRole(int $userId, array $roleIds): bool
{
    $result = $this->updateDatabase($userId, $roleIds);
    
    if ($result) {
        // 清理用户权限缓存
        Cache::tag('permission')->clear();
        // 清理菜单缓存
        Cache::tag('menu')->clear();
    }
    
    return $result;
}
```

### 3. 并发处理

#### 分布式锁
```php
public function approveTask(int $taskId): bool
{
    $lockKey = "task_approve:{$taskId}";
    
    return Cache::lock($lockKey, 30)->get(function () use ($taskId) {
        // 防止重复审批
        $task = WorkflowTask::find($taskId);
        if ($task->status != 0) {
            throw new BusinessException('任务已被处理');
        }
        
        // 执行审批逻辑
        return $this->doApprove($task);
    });
}
```

## 📊 监控和日志

### 1. 业务监控

#### 关键指标监控
```php
// 审批效率监控
class WorkflowMetrics
{
    public function recordApprovalTime(int $instanceId, int $duration): void
    {
        // 记录审批耗时
        Log::info('审批耗时统计', [
            'instance_id' => $instanceId,
            'duration' => $duration,
            'timestamp' => time()
        ]);
    }
    
    public function recordApprovalResult(string $result): void
    {
        // 记录审批结果统计
        Cache::increment("approval_result:{$result}");
    }
}
```

### 2. 错误监控

#### 异常处理和上报
```php
class WorkflowExceptionHandler
{
    public function handle(\Exception $e): void
    {
        // 记录详细错误信息
        Log::error('工作流异常', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'context' => $this->getContext()
        ]);
        
        // 发送告警通知
        $this->sendAlert($e);
    }
}
```

## 🔒 安全设计

### 1. 权限安全

#### 操作权限验证
```php
public function approveTask(int $taskId): bool
{
    $task = WorkflowTask::find($taskId);
    
    // 验证任务存在
    if (!$task) {
        throw new BusinessException('任务不存在');
    }
    
    // 验证操作权限
    if ($task->approver_id != request()->adminId && !is_super_admin()) {
        throw new PermissionException('无权操作此任务');
    }
    
    // 验证任务状态
    if ($task->status != 0) {
        throw new BusinessException('任务状态不允许操作');
    }
    
    return $this->doApprove($task);
}
```

### 2. 数据安全

#### SQL注入防护
```php
// 使用参数化查询
$tasks = WorkflowTask::where('approver_id', $userId)
                    ->where('status', 0)
                    ->select();

// 避免直接拼接SQL
// 错误示例：$sql = "SELECT * FROM workflow_task WHERE approver_id = {$userId}";
```

#### XSS防护
```php
// 输出时进行HTML转义
public function getApprovalOpinion(): string
{
    return htmlspecialchars($this->opinion, ENT_QUOTES, 'UTF-8');
}
```

---

**文档更新日期**：2025-01-24  
**版权所有**：Base Admin 系统

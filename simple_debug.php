<?php
/**
 * 简单的数据库调试脚本
 */

// 从 .env 文件读取数据库配置
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    $lines = explode("\n", $envContent);
    $config = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $config[trim($key)] = trim($value);
        }
    }
    
    // 数据库连接参数
    $host = $config['DB_HOST'] ?? '127.0.0.1';
    $dbname = $config['DB_NAME'] ?? '';
    $username = $config['DB_USER'] ?? '';
    $password = $config['DB_PASS'] ?? '';
    $port = $config['DB_PORT'] ?? '3306';
    
    echo "=== 角色菜单关联调试 ===\n\n";
    echo "数据库配置：\n";
    echo "Host: {$host}:{$port}\n";
    echo "Database: {$dbname}\n";
    echo "Username: {$username}\n\n";
    
    try {
        // 创建 PDO 连接
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "✅ 数据库连接成功！\n\n";
        
        // 1. 查看角色表数据
        echo "1. 查看角色表数据...\n";
        $stmt = $pdo->prepare("SELECT id, name, tenant_id, status FROM system_role WHERE tenant_id = 1 AND status = 1 LIMIT 5");
        $stmt->execute();
        $roles = $stmt->fetchAll();
        
        echo "角色数量：" . count($roles) . "\n";
        foreach ($roles as $role) {
            echo "角色ID：{$role['id']}, 名称：{$role['name']}, 租户ID：{$role['tenant_id']}\n";
        }
        echo "\n";
        
        // 2. 查看角色菜单关联表数据
        echo "2. 查看角色菜单关联表数据...\n";
        $stmt = $pdo->prepare("SELECT id, role_id, menu_id, tenant_id FROM system_role_menu WHERE tenant_id = 1 LIMIT 10");
        $stmt->execute();
        $roleMenus = $stmt->fetchAll();
        
        echo "角色菜单关联数量：" . count($roleMenus) . "\n";
        foreach ($roleMenus as $rm) {
            echo "关联ID：{$rm['id']}, 角色ID：{$rm['role_id']}, 菜单ID：{$rm['menu_id']}, 租户ID：{$rm['tenant_id']}\n";
        }
        echo "\n";
        
        // 3. 检查特定角色的菜单权限
        if (!empty($roles)) {
            $testRoleId = $roles[0]['id'];
            echo "3. 检查角色ID {$testRoleId} 的菜单权限...\n";
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
            $stmt->execute([$testRoleId]);
            $result = $stmt->fetch();
            $roleMenuCount = $result['count'];
            
            echo "该角色的菜单权限数量：{$roleMenuCount}\n";
            
            if ($roleMenuCount > 0) {
                $stmt = $pdo->prepare("SELECT menu_id FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
                $stmt->execute([$testRoleId]);
                $menuIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                echo "菜单ID列表：" . implode(', ', $menuIds) . "\n";
            }
        }
        
        // 4. 模拟角色更新操作
        if (!empty($roles)) {
            $testRoleId = $roles[0]['id'];
            echo "\n4. 模拟角色更新操作（角色ID: {$testRoleId}）...\n";
            
            // 记录更新前的状态
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
            $stmt->execute([$testRoleId]);
            $beforeCount = $stmt->fetch()['count'];
            echo "更新前菜单权限数量：{$beforeCount}\n";
            
            // 模拟删除操作
            $stmt = $pdo->prepare("DELETE FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
            $deleteResult = $stmt->execute([$testRoleId]);
            echo "删除操作结果：" . ($deleteResult ? "成功" : "失败") . "\n";
            
            // 检查删除后的状态
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
            $stmt->execute([$testRoleId]);
            $afterDeleteCount = $stmt->fetch()['count'];
            echo "删除后菜单权限数量：{$afterDeleteCount}\n";
            
            // 模拟插入新的菜单权限
            $testMenuIds = [78, 163, 164, 162, 160];
            echo "准备插入菜单ID：" . implode(', ', $testMenuIds) . "\n";
            
            $insertStmt = $pdo->prepare("INSERT INTO system_role_menu (role_id, menu_id, tenant_id, creator_id, created_at, updated_at) VALUES (?, ?, 1, 1, NOW(), NOW())");
            
            $insertCount = 0;
            foreach ($testMenuIds as $menuId) {
                if ($insertStmt->execute([$testRoleId, $menuId])) {
                    $insertCount++;
                }
            }
            
            echo "成功插入菜单权限数量：{$insertCount}\n";
            
            // 检查最终状态
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_role_menu WHERE role_id = ? AND tenant_id = 1");
            $stmt->execute([$testRoleId]);
            $finalCount = $stmt->fetch()['count'];
            echo "最终菜单权限数量：{$finalCount}\n";
            
            if ($finalCount == count($testMenuIds)) {
                echo "✅ 模拟更新操作成功！\n";
            } else {
                echo "❌ 模拟更新操作失败！\n";
            }
        }
        
        echo "\n=== 调试完成 ===\n";
        
    } catch (PDOException $e) {
        echo "❌ 数据库连接失败：" . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ 未找到 .env 配置文件\n";
}

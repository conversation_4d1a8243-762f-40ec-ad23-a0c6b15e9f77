<?php
declare(strict_types=1);

namespace app\workflow\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\workflow\model\WorkflowType;
use app\workflow\service\WorkflowDefinitionService;
use think\response\Json;

/**
 * 工作流程定义表控制器
 */
class DefinitionController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var WorkflowDefinitionService
	 */
	protected WorkflowDefinitionService $service;
	
	/**
	 * 构造函数
	 */
	public function initialize(): void
	{
		parent::initialize();
		$this->service = WorkflowDefinitionService::getInstance();
		$this->service->setEnableDataPermission(false);
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$result = $this->service->search($this->request->param(), [], ['types']);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 新增
	 *
	 * @return Json
	 */
	public function add(): Json
	{
		$params = $this->request->post();
		$typeId = $params['type_id'];
		if (!$typeId) {
			return $this->error('参数错误');
		}
		$info = (new WorkflowType())->where([
			'id' => $typeId
		])
		                            ->findOrEmpty();
		if ($info->isEmpty()) {
			return $this->error('流程类型不存在');
		}
		$definition = $this->service->getOne([
			[
				'type_id',
				'=',
				$typeId
			]
		], [], false);
		if (!$definition->isEmpty()) {
			return $this->error('同类型请勿重复添加');
		}
		$result = $this->service->add($params);
		return $result
			? $this->success('更新成功')
			: $this->error('更新失败');
	}
	
	/**
	 * 获取不分页列表
	 *
	 * @return Json
	 */
	public function all(): Json
	{
		$result = $this->service->getModel()
		                        ->with(['types'])
		                        ->where([
			                        [
				                        'status',
				                        '=',
				                        1
			                        ]
		                        ], ['created_at' => 'desc'], ['types'])
		                        ->select();
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 删除
	 */
	public function delete(): Json
	{
		$ids = $this->request->post('ids');
		if (!$ids) {
			return $this->error('参数错误');
		}
		return $this->service->delete($ids)
			? $this->success()
			: $this->error();
	}
} 
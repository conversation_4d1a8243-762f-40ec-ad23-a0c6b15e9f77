<template>
  <div class="page-content">
    <el-tabs v-model="activeTab" type="card" class="settings-tabs" v-loading="loading">
      <el-tab-pane label="自动退回公海规则" name="sea_rule">
        <el-form label-width="120px">
          <el-form-item label="是否开启：">
            <el-switch
              v-model="seaRule.sea_status"
              :disabled="!isEditSeaRule"
              inline-prompt
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="未跟进天数：">
            <el-input v-model="seaRule.follow_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于设置未成交状态的客户，多少天未跟进自动退回公海</div>
          </el-form-item>
          <el-form-item label="未成交天数：">
            <el-input v-model="seaRule.deal_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于设置客户在未成交状态滞留多少天自动退回公海</div>
          </el-form-item>
          <el-form-item label="退回公海提醒：">
            <el-input v-model="seaRule.notify_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于客户退回公海提前多少天进行提醒</div>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="isEditSeaRule"
              style="width: 90px"
              v-ripple
              @click="isEditSeaRule = false"
            >
              取消
            </el-button>
            <el-button type="primary" style="width: 90px" v-ripple @click="editSeaRuleClick">
              {{ isEditSeaRule ? '保存' : '编辑' }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { onMounted, ref } from 'vue'
  import { TenantConfigApi } from '@/api/TenantConfigApi'
  import { ApiStatus } from '@/utils/http/status'
  import { isEmpty, convertSeaRuleConfigTypes } from '@/utils/utils'

  // 当前选中的标签页
  const activeTab = ref('sea_rule')

  // 加载状态
  const loading = ref(false)

  // 网站配置数据初始化
  const seaRule = ref({
    sea_status: 0,
    follow_days: 15,
    deal_days: 30,
    notify_days: 3
  })

  // 页面加载时初始化系统信息
  onMounted(async () => {
    await getConfig()
  })

  // 编辑状态控制
  const isEditSeaRule = ref(false)

  // 获取系统配置
  const getConfig = async () => {
    try {
      loading.value = true
      const res = await TenantConfigApi.getTenantConfigDetail()
      if (res.code === ApiStatus.success && res.data) {
        if (!isEmpty(res.data)) {
          // 处理公海规则配置，对后端返回的字符串类型数据进行类型转换
          if (res.data.sea_rule) {
            // 使用工具函数进行类型转换
            seaRule.value = convertSeaRuleConfigTypes(res.data.sea_rule)
          } else {
            // 如果没有 sea_rule 数据，使用默认值
            seaRule.value = {
              sea_status: 0,
              follow_days: 15,
              deal_days: 30,
              notify_days: 3
            }
          }
        }
      }
    } catch (error) {
      console.error('获取系统配置失败:', error)
      ElMessage.error('获取系统配置失败')
    } finally {
      loading.value = false
    }
  }

  // 编辑网站配置
  const editSeaRuleClick = async () => {
    if (isEditSeaRule.value) {
      // 保存网站配置逻辑
      try {
        loading.value = true
        const res = await TenantConfigApi.saveTenantConfig({
          group: 'sea_rule',
          config: seaRule.value
        })
        if (res.code === ApiStatus.success) {
          ElMessage.success('配置保存成功')
        }
      } catch (error) {
        console.error('保存网站配置失败:', error)
        ElMessage.error('保存网站配置失败')
        return
      } finally {
        loading.value = false
      }
    }
    isEditSeaRule.value = !isEditSeaRule.value
  }
</script>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;

    .header {
      padding-bottom: 15px;
      border-bottom: 1px solid var(--art-border-color);

      h3 {
        font-size: 18px;
        font-weight: 500;
      }
    }

    .settings-tabs {
      margin-top: 20px;

      :deep(.el-tabs__header) {
        margin-bottom: 20px;
      }

      :deep(.el-tabs__nav) {
        border-radius: 4px;
      }

      :deep(.el-tabs__item) {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }
    }

    .oss-config-title {
      font-size: 16px;
      font-weight: 500;
      margin: 10px 0 20px;
      padding-bottom: 10px;
      border-bottom: 1px dashed var(--art-border-color);
      color: var(--art-text-color);
    }

    :deep(.el-form) {
      max-width: 800px;
      margin: 0 auto;
    }
  }
</style>

<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/daily', function () {
	
	
	$nameSpace = 'app\daily\controller';
	
	// 每日报价相关路由（从Router.php迁移）
	Route::get('daily_price_order/check_duplicate_date', $nameSpace . '\DailyPriceOrderController@checkDuplicateDate');
	Route::get('daily_price_order/get_yesterday_prices', $nameSpace . '\DailyPriceOrderController@getYesterdayPrices');
	
	// 预留：其他每日报价功能路由
	// Route::get('daily_price_order/index', $nameSpace . '\DailyPriceOrderController@index');
	// Route::post('daily_price_order/add', $nameSpace . '\DailyPriceOrderController@add');
	// Route::post('daily_price_order/edit/:id', $nameSpace . '\DailyPriceOrderController@edit');
	// Route::post('daily_price_order/delete', $nameSpace . '\DailyPriceOrderController@delete');
	
	// 预留：其他每日报价模块路由
	// 根据实际控制器文件添加相应路由
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);

import request from '@/utils/http'
import type { BaseResult } from '@/types/axios'

/**
 * 工作台API接口
 */
export class WorkbenchApi {
  /**
   * 获取关键指标统计
   */
  static getKeyStatistics() {
    return request.get<BaseResult>({
      url: '/dashboard/getKeyStatistics'
    })
  }

  /**
   * 获取聚合待办任务
   */
  static getTodoTasks() {
    return request.get<BaseResult>({
      url: '/dashboard/getTodoTasks'
    })
  }

  /**
   * 获取工作汇报摘要
   */
  static getWorkReports(params?: { limit?: number }) {
    return request.get<BaseResult>({
      url: '/dashboard/getWorkReports',
      params
    })
  }

  /**
   * 获取企业新闻
   */
  static getCompanyNews(params?: { limit?: number }) {
    return request.get<BaseResult>({
      url: '/dashboard/getCompanyNews',
      params
    })
  }

  /**
   * 获取工作台所有数据（一次性接口）
   */
  static getWorkbenchData() {
    return request.get<BaseResult>({
      url: '/dashboard/getWorkbenchData'
    })
  }
}

/**
 * 工作台数据类型定义
 */
export interface KeyStatistics {
  customer_stats: {
    total: number
    monthly_new: number
    growth: number
    growth_class: string
    growth_text: string
  }
  contract_stats: {
    total_amount: number
    total_amount_text: string
    monthly_amount: number
    monthly_amount_text: string
    completion_rate: number
    completion_rate_text: string
  }
  project_stats: {
    in_progress: number
    completed: number
    total: number
    completion_rate: number
    completion_rate_text: string
  }
}

export interface TodoTask {
  id: number
  type: 'workflow' | 'crm' | 'project'
  title: string
  time: string
  urgent: boolean
  extra: Record<string, any>
}

export interface TodoTasksData {
  tasks: TodoTask[]
  total_count: number
  urgent_count: number
  type_counts: {
    workflow: number
    crm: number
    project: number
  }
}

export interface WorkReport {
  id: number
  title: string
  content: string
  report_type: number
  type_text: string
  summary: string
  reporter_name: string
  created_at: string
  created_at_text: string
}

export interface CompanyNews {
  id: number
  title: string
  summary: string
  is_important: number | boolean
  is_top?: number | boolean
  created_at: string
  created_at_text?: string
  publish_time?: string
  cover_image?: string[]
  category_id?: number
  category_name?: string
}

export interface WorkbenchData {
  key_statistics: KeyStatistics
  todo_tasks: TodoTasksData
  work_reports: WorkReport[]
  company_news: CompanyNews[]
}

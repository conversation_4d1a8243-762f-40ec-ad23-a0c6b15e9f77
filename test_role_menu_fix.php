<?php
/**
 * 测试角色菜单关联修复脚本
 * 用于验证角色编辑时菜单权限是否正确保存
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/app/');
require __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

// 模拟请求环境
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/test';

use app\system\service\RoleService;
use think\facade\Db;
use think\facade\Request;

// 模拟请求数据
Request::withInput([
    'tenantId' => 1,
    'adminId' => 1,
    'adminInfo' => [
        'admin_id' => 1,
        'data' => [
            'id' => 1,
            'username' => 'admin',
            'tenant_id' => 1
        ]
    ]
]);

try {
    echo "开始测试角色菜单关联修复...\n\n";
    
    // 1. 查找一个测试角色
    $roleInfo = Db::table('system_role')
        ->where('tenant_id', 1)
        ->where('status', 1)
        ->find();
    
    if (!$roleInfo) {
        echo "错误：未找到测试角色\n";
        exit(1);
    }
    
    $roleId = $roleInfo['id'];
    echo "使用测试角色：ID={$roleId}, Name={$roleInfo['name']}\n";
    
    // 2. 查看修复前的角色菜单关联数量
    $beforeCount = Db::table('system_role_menu')
        ->where('role_id', $roleId)
        ->where('tenant_id', 1)
        ->count();
    
    echo "修复前角色菜单关联数量：{$beforeCount}\n";
    
    // 3. 准备测试数据（模拟前端提交的数据）
    $testData = [
        'name' => $roleInfo['name'],
        'data_scope' => 1,
        'data_scope_dept_ids' => [],
        'menu_ids' => [78, 163, 164, 162, 160, 161, 147], // 测试少量菜单ID
        'sort' => 1,
        'status' => 1,
        'remark' => '测试角色菜单关联修复'
    ];
    
    echo "准备更新角色，菜单ID数量：" . count($testData['menu_ids']) . "\n";
    
    // 4. 执行角色更新
    $roleService = RoleService::getInstance();
    $result = $roleService->update($roleId, 1, $testData);
    
    if ($result) {
        echo "角色更新成功！\n";
        
        // 5. 查看修复后的角色菜单关联数量
        $afterCount = Db::table('system_role_menu')
            ->where('role_id', $roleId)
            ->where('tenant_id', 1)
            ->count();
        
        echo "修复后角色菜单关联数量：{$afterCount}\n";
        
        // 6. 验证菜单ID是否正确保存
        $savedMenuIds = Db::table('system_role_menu')
            ->where('role_id', $roleId)
            ->where('tenant_id', 1)
            ->column('menu_id');
        
        echo "保存的菜单ID：" . implode(', ', $savedMenuIds) . "\n";
        
        // 7. 检查是否与预期一致
        $expectedMenuIds = $testData['menu_ids'];
        sort($expectedMenuIds);
        sort($savedMenuIds);
        
        if ($expectedMenuIds === $savedMenuIds) {
            echo "✅ 测试通过：菜单权限保存正确！\n";
        } else {
            echo "❌ 测试失败：菜单权限保存不正确！\n";
            echo "期望的菜单ID：" . implode(', ', $expectedMenuIds) . "\n";
            echo "实际保存的菜单ID：" . implode(', ', $savedMenuIds) . "\n";
        }
        
    } else {
        echo "❌ 角色更新失败！\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误：" . $e->getMessage() . "\n";
    echo "错误堆栈：\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成。\n";

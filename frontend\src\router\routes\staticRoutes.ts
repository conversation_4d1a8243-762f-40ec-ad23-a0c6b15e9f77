import { AppRouteRecordRaw } from '../utils/utils'
import { RoutesAlias, HOME_PAGE } from '../routesAlias'
import Home from '@views/index/index.vue'
// import { workflowRoutes } from './workflowRoutes'

/**
 * 静态路由配置
 * 不需要权限就能访问的路由
 */
export const staticRoutes: AppRouteRecordRaw[] = [
  {
    path: '/',
    redirect: HOME_PAGE
  },
  {
    path: RoutesAlias.Login,
    name: 'Login',
    component: () => import('@views/login/index.vue'),
    meta: { title: 'menus.login.title', isHideTab: true, setTheme: true, noMenuCheck: true }
  },
  {
    path: '/test/attachment',
    name: 'AttachmentTest',
    component: () => import('@views/test/AttachmentTest.vue'),
    meta: { title: '附件管理测试', isHideTab: false, noMenuCheck: true }
  },
  {
    path: '/test/media-selector',
    name: 'MediaSelectorTest',
    component: () => import('@views/test/MediaSelectorTest.vue'),
    meta: { title: 'MediaSelector测试', isHideTab: false, noMenuCheck: true }
  },
  {
    path: '/test/news-demo',
    name: 'NewsDemo',
    component: () => import('@views/test/NewsDemo.vue'),
    meta: { title: '新闻资讯UI美化演示', isHideTab: false, noMenuCheck: true }
  },
  /*{
    path: RoutesAlias.Register,
    name: 'Register',
    component: () => import('@views/register/index.vue'),
    meta: { title: 'menus.register.title', isHideTab: true, noLogin: true, setTheme: true }
  },
  {
    path: RoutesAlias.ForgetPassword,
    name: 'ForgetPassword',
    component: () => import('@views/forget-password/index.vue'),
    meta: { title: 'menus.forgetPassword.title', isHideTab: true, noLogin: true, setTheme: true }
  },*/
  {
    path: '/exception',
    component: Home,
    name: 'Exception',
    meta: { title: 'menus.exception.title', noMenuCheck: true },
    children: [
      {
        path: RoutesAlias.Exception403,
        name: 'Exception403',
        component: () => import('@views/exception/403.vue'),
        meta: { title: '403', noMenuCheck: true }
      },
      {
        path: RoutesAlias.Exception404,
        name: 'Exception404',
        component: () => import('@views/exception/404.vue'),
        meta: { title: '404', noMenuCheck: true }
      },
      {
        path: RoutesAlias.Exception500,
        name: 'Exception500',
        component: () => import('@views/exception/500.vue'),
        meta: { title: '500', noMenuCheck: true }
      }
    ]
  },
  {
    path: '/outside',
    component: Home,
    name: 'Outside',
    meta: { title: 'menus.outside.title' },
    children: [
      {
        path: '/outside/iframe/:path',
        name: 'Iframe',
        component: () => import('@/views/outside/Iframe.vue'),
        meta: { title: 'iframe' }
      }
    ]
  },
  // 项目管理路由
  /*{
    path: '/project',
    component: Home,
    name: 'Project',
    meta: { title: '项目管理' },
    children: [
      {
        path: '/project/detail/:id',
        name: 'ProjectDetail',
        component: () => import('@/views/project/ProjectDetail.vue'),
        meta: { title: '项目详情', isHideTab: true, keepAlive: false }
      }
    ]
  },*/
  // HR系统路由
  {
    path: '/hr',
    component: Home,
    name: 'Hr',
    meta: { title: 'HR管理' },
    children: [
      {
        path: '/hr/monthly-stats',
        name: 'HrMonthlyStats',
        component: () => import('@/views/hr/monthly-stats/index.vue'),
        meta: { title: '假勤月度统计' }
      }
    ]
  }
  // ...workflowRoutes
]

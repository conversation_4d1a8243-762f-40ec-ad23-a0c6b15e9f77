<?php

namespace app\system\service;

use app\common\core\constants\SystemConstant;
use app\system\model\AdminModel;
use app\system\model\RoleMenuModel;

class PermissionService
{
	public function getAdminPermissionByAdminId($adminId, $iaAll = false)
	{
		$permission = [];
		
		// 获取当前用户的角色ID
		$adminInfo = (new AdminModel())->with(['roles'])
		                               ->where([
			                               [
				                               'id',
				                               '=',
				                               $adminId
			                               ],
			                               [
				                               'status',
				                               '=',
				                               1
			                               ],
		                               ])
		                               ->findOrEmpty();
		
		if ($adminInfo->isEmpty()) {
			return $permission;
		}
		
		$where = [];
		
		if ($adminId != SystemConstant::SUPER_ADMIN_ID) {
			
			if ($adminInfo['roles']->isEmpty()) {
				return $permission;
			}
			
			$roleIds = $adminInfo['roles']->column('role_id');
			
			if (empty($roleIds)) {
				return $permission;
			}
			
			// 获取用户实际选择的菜单ID（去重处理）
			$userSelectedMenuIds = array_unique(RoleMenuModel::where([
				[
					'role_id',
					'in',
					$roleIds
				],
			])
			                        ->column('menu_id'));

			if (empty($userSelectedMenuIds)) {
				return $permission;
			}

			// 动态包含所有必要的父节点
			$allRequiredMenuIds = $this->includeParentMenuIds($userSelectedMenuIds);

			$where = [
				[
					'id',
					'in',
					array_unique($allRequiredMenuIds)
				],
				[
					'status',
					'=',
					1
				],
			];
			
		}
		
		$menuList = MenuService::getInstance()
		                       ->getModel()
		                       ->where($where)
		                       ->order('sort desc')
		                       ->select();
		
		return $iaAll
			? $menuList
			: $menuList->column('name');
	}

	/**
	 * 动态包含所有必要的父节点
	 *
	 * @param array $selectedMenuIds 用户实际选择的菜单ID
	 * @return array 包含父节点的完整菜单ID列表
	 */
	private function includeParentMenuIds(array $selectedMenuIds): array
	{
		if (empty($selectedMenuIds)) {
			return [];
		}

		// 获取所有菜单的父子关系
		$allMenus = MenuService::getInstance()->getModel()
			->field('id,parent_id')
			->where('status', 1)
			->select()
			->toArray();

		// 构建父子关系映射
		$parentMap = [];
		foreach ($allMenus as $menu) {
			$parentMap[$menu['id']] = $menu['parent_id'];
		}

		$allRequiredIds = $selectedMenuIds;

		// 为每个选中的菜单添加其所有父节点
		foreach ($selectedMenuIds as $menuId) {
			$parentId = $parentMap[$menuId] ?? 0;
			while ($parentId > 0) {
				if (!in_array($parentId, $allRequiredIds)) {
					$allRequiredIds[] = $parentId;
				}
				$parentId = $parentMap[$parentId] ?? 0;
			}
		}

		return $allRequiredIds;
	}

	/**
	 * 解析权限信息
	 *
	 * @param string $ruleName 请求的控制器方法标识
	 * @return array 包含模块、控制器和方法的关联数组
	 */
	public function parsePermissionInfo(string $ruleName): array
	{
		// 分割类路径和方法名
		[
			$classPath,
			$method
		] = explode('@', $ruleName);
		
		// 分割类路径为命名空间部分
		$parts = explode('\\', $classPath);
		
		// 提取模块名（app\模块\controller\...）
		$module = strtolower($parts[1]); // 第二个部分是模块名
		
		// 提取控制器名（最后一个命名空间部分）
		$controllerClass = $parts[count($parts) - 1];
		
		// 去掉Controller后缀
		$controllerName = str_replace('Controller', '', $controllerClass);
		
		// 根据数据库中的权限标识格式生成权限名称
		$permissionName = $this->generatePermissionName($module, $parts, $controllerName, $method);
		
		// 返回解析后的信息
		return [
			$module,
			$permissionName,
			// 这里已经包含了完整的权限路径
			$method
		];
	}
	
	/**
	 * 生成权限名称（统一规范）
	 *
	 * @param string $module         模块名
	 * @param array  $parts          路径部分
	 * @param string $controllerName 控制器名（不含Controller后缀）
	 * @param string $method         方法名
	 * @return string 权限名称
	 */
	private function generatePermissionName(string $module, array $parts, string $controllerName, string $method
	): string
	{
		// 统一的权限解析逻辑，不再区分模块
		$snakeName = $this->camelToSnake($controllerName);
		
		// 将方法名也转换为下划线格式，保持命名风格统一
		$methodSnakeName = $this->camelToSnake($method);
		
		// 处理子目录控制器
		if (count($parts) > 4) {
			$subPath = strtolower($parts[3]);
			return $subPath . '_' . $snakeName . ':' . $methodSnakeName;
		}
		
		// 基础控制器直接使用控制器名
		return $snakeName . ':' . $methodSnakeName;
	}
	
	
	/**
	 * 将驼峰命名转换为下划线命名
	 */
	private function camelToSnake(string $input): string
	{
		return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
	}
	
	
}
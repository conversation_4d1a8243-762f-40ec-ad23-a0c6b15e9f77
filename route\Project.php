<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/project', function () {
	
	
	$nameSpace = 'app\project\controller';
	
	// 项目管理模块路由将在此处添加
	// 基于现有控制器文件结构，需要添加相应的路由定义
	
	// 预留：项目管理相关路由
	// Route::get('project/index', $nameSpace . '\ProjectController@index');
	// Route::post('project/add', $nameSpace . '\ProjectController@add');
	// Route::post('project/edit/:id', $nameSpace . '\ProjectController@edit');
	// Route::post('project/delete', $nameSpace . '\ProjectController@delete');
	// Route::get('project/detail/:id', $nameSpace . '\ProjectController@detail');
	
	// 预留：项目成员相关路由
	// Route::get('member/index', $nameSpace . '\ProjectMemberController@index');
	// Route::post('member/add', $nameSpace . '\ProjectMemberController@add');
	// Route::post('member/edit/:id', $nameSpace . '\ProjectMemberController@edit');
	// Route::post('member/delete', $nameSpace . '\ProjectMemberController@delete');
	
	// 预留：项目任务相关路由
	// Route::get('task/index', $nameSpace . '\ProjectTaskController@index');
	// Route::post('task/add', $nameSpace . '\ProjectTaskController@add');
	// Route::post('task/edit/:id', $nameSpace . '\ProjectTaskController@edit');
	// Route::post('task/delete', $nameSpace . '\ProjectTaskController@delete');
	
	// 预留：项目文档相关路由
	// Route::get('document/index', $nameSpace . '\ProjectDocumentController@index');
	// Route::post('document/add', $nameSpace . '\ProjectDocumentController@add');
	// Route::post('document/edit/:id', $nameSpace . '\ProjectDocumentController@edit');
	// Route::post('document/delete', $nameSpace . '\ProjectDocumentController@delete');
	
	// 预留：其他项目功能路由
	// 根据实际控制器文件添加相应路由
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);

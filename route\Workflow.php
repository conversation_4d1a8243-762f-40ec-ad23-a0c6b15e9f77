<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

//$nameSpace = 'app\workflow\controller';

/*Route::group('api/workflow', function () use ($nameSpace) {
	
	// 我的申请列表
	Route::get('application', $nameSpace . '/ApplicationController@index');
	// 申请详情
	Route::get('application/:id', $nameSpace . '/ApplicationController@detail');
	// 撤回申请
	Route::post('application/withdraw/:id', $nameSpace . '/ApplicationController@withdraw');
	// 删除申请
	Route::delete('application/:id', $nameSpace . '/ApplicationController@delete');
	// 提交请假申请
	Route::post('leave/submit', $nameSpace . '/ApplicationController@submitLeave');
	// 提交出差申请
	Route::post('travel/submit', $nameSpace . '/ApplicationController@submitTravel');
	// 表单管理相关路由
	//	Route::get('form/index', $nameSpace . '\FormController@index');
	//	Route::get('form/detail/:id', $nameSpace . '\FormController@detail');
	//	Route::post('form/add', $nameSpace . '\FormController@add');
	//	Route::post('form/edit/:id', $nameSpace . '\FormController@edit');
	//	Route::post('form/delete', $nameSpace . '\FormController@delete');
	// 以下路由暂未实现或已废弃
	//	Route::post('form/disable', $nameSpace . '\FormController@disable');
	//	Route::post('form/copy', $nameSpace . '\FormController@copy');
	
	// 流程定义管理相关路由
	Route::get('definition/index', $nameSpace . '\DefinitionController@index');
	Route::get('definition/detail/:id', $nameSpace . '\DefinitionController@detail');
	Route::post('definition/add', $nameSpace . '\DefinitionController@add');
	Route::post('definition/edit/:id', $nameSpace . '\DefinitionController@edit');
	Route::post('definition/delete', $nameSpace . '\DefinitionController@delete');
	
	Route::get('definition/moduleOptions', $nameSpace . '\DefinitionController@moduleOptions');
	Route::post('definition/flowSave/:id', $nameSpace . '\DefinitionController@flowSave');
	
	// 以下路由暂未实现或已废弃
	//	Route::post('definition/disable', $nameSpace . '\DefinitionController@disable');
	//	Route::post('definition/copy', $nameSpace . '\DefinitionController@copy');
	
	// 流程实例管理相关路由
	Route::get('instance/index', $nameSpace . '\InstanceController@index');
	Route::get('instance/detail/:id', $nameSpace . '\InstanceController@detail');
	Route::post('instance/start', $nameSpace . '\InstanceController@start');
	Route::post('instance/recall', $nameSpace . '\InstanceController@recall');
	Route::post('instance/terminate', $nameSpace . '\InstanceController@terminate');
	Route::get('instance/myStarted', $nameSpace . '\InstanceController@myStarted');
	Route::post('instance/approve', $nameSpace . '\InstanceController@approve');
	
	// 任务管理相关路由
	Route::get('task/todo', $nameSpace . '\TaskController@todo');
	Route::get('task/done', $nameSpace . '\TaskController@done');
	Route::get('task/all', $nameSpace . '\TaskController@all');
	Route::get('task/cc', $nameSpace . '\TaskController@cc');
	Route::get('task/canceled', $nameSpace . '\TaskController@canceled');
	Route::get('task/detail/:id', $nameSpace . '\TaskController@detail');
	Route::post('task/approve', $nameSpace . '\TaskController@approve');
	Route::post('task/reject', $nameSpace . '\TaskController@reject');
	Route::post('task/transfer', $nameSpace . '\TaskController@transfer');
	Route::post('task/urge', $nameSpace . '\TaskController@urge');
	Route::post('task/backToNode', $nameSpace . '\TaskController@backToNode');
	
	// 抄送管理相关路由
	Route::get('cc/myList', $nameSpace . '\CcController@myList');
	Route::post('cc/read', $nameSpace . '\CcController@read');
	Route::post('cc/batchRead', $nameSpace . '\CcController@batchRead');
	Route::get('cc/detail', $nameSpace . '\CcController@detail');
	
	// 报表统计相关路由
	Route::get('report/overview', $nameSpace . '\ReportController@overview');
	Route::get('report/typeStats', $nameSpace . '\ReportController@typeStats');
	Route::get('report/efficiencyStats', $nameSpace . '\ReportController@efficiencyStats');
	Route::get('report/personalStats', $nameSpace . '\ReportController@personalStats');
	
	// 流程类型相关路由
	Route::get('type/index', $nameSpace . '\TypeController/index');
	Route::get('type/detail/:id', $nameSpace . '\TypeController/detail');
	Route::post('type/add', $nameSpace . '\TypeController/add');
	Route::post('type/edit/:id', $nameSpace . '\TypeController/edit');
	Route::post('type/delete', $nameSpace . '\TypeController/delete');
	
	// 以下路由暂未实现或已废弃
	//	Route::post('updateField', $nameSpace . '\TypeController/updateField');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
     ]);

Route::get('api/workflow/type/options', $nameSpace . '\TypeController@options')
     ->middleware(TokenAuthMiddleware::class);*/


$router = 'api/workflow';
Route::group($router, function () {
	
	$nameSpace = 'app\workflow\controller';
	// 工作流程类型表路由
	Route::get('type/index', $nameSpace . '\TypeController@index');
	Route::get('type/detail/:id', $nameSpace . '\TypeController@detail');
	Route::post('type/add', $nameSpace . '\TypeController@add');
	Route::post('type/edit/:id', $nameSpace . '\TypeController@edit');
	Route::post('type/delete', $nameSpace . '\TypeController@delete');
	Route::post('type/status/:id', $nameSpace . '\TypeController@status');
	
	
	// 工作流程定义表路由
	Route::get('definition/index', $nameSpace . '\DefinitionController@index');
	
	Route::get('definition/detail/:id', $nameSpace . '\DefinitionController@detail');
	Route::post('definition/add', $nameSpace . '\DefinitionController@add');
	Route::post('definition/edit/:id', $nameSpace . '\DefinitionController@edit');
	Route::post('definition/delete', $nameSpace . '\DefinitionController@delete');
	Route::post('definition/updateField', $nameSpace . '\DefinitionController@updateField');
	
	// 我的申请
	Route::get('myapp/index', $nameSpace . '\ApplicationController@index');
	// 获取详情
	Route::get('myapp/detail/:id', $nameSpace . '\ApplicationController@detail');
	// 保存
	Route::post('myapp/save', $nameSpace . '\ApplicationController@save');
	// 提交
	Route::post('myapp/submit', $nameSpace . '\ApplicationController@submit');
	// 确认提交
	Route::post('myapp/confirm/:id', $nameSpace . '\ApplicationController@confirm');
	// 修改
	Route::post('myapp/edit/:id', $nameSpace . '\ApplicationController@edit');
	// 删除
	//	Route::post('myapp/delete/:id', $nameSpace .'\ApplicationController@delete');
	// 撤回
	Route::post('myapp/recall/:id', $nameSpace . '\ApplicationController@recall');
	// 作废
	Route::post('myapp/void/:id', $nameSpace . '\ApplicationController@void');
	
	// 工作流程任务表路由
	Route::get('task/index', $nameSpace . '\TaskController@index');
	Route::get('task/detail/:id', $nameSpace . '\TaskController@detail');
	
	Route::post('task/approve', $nameSpace . '\TaskController@approve');
	Route::post('task/reject', $nameSpace . '\TaskController@reject');
	Route::post('task/transfer', $nameSpace . '\TaskController@transfer');
	Route::post('task/urge', $nameSpace . '\TaskController@urge');
	Route::post('task/terminate', $nameSpace . '\TaskController@terminate');
	
	// 抄送
	Route::get('cc/index', $nameSpace . '\CcController@index');
	Route::get('cc/detail', $nameSpace . '\CcController@detail');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
	     //	     OperationLogMiddleware::class
     ]);

Route::get($router . '/definition/all', 'app\workflow\controller\DefinitionController@all')
     ->middleware([
	     TokenAuthMiddleware::class,
	     //	     OperationLogMiddleware::class
     ]);